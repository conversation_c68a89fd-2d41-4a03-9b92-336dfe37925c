/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import lombok.extern.slf4j.Slf4j;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.publisher.FrontendErrorLogRequest;

/**
 * Controller for logging frontend.
 *
 * <AUTHOR> Ren
 */
@Slf4j
public class LogController extends Controller {

    /**
     * Log the frontend error message extracted from request body.
     */
    public void logFrontendError() {
        FrontendErrorLogRequest error = getRouteContext().getRequest()
                .createEntityFromBody(FrontendErrorLogRequest.class);

        log.error(error.getMessage());

        getRouteContext().getResponse().ok();
    }
}
