/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.auth.v1.RequestAuthenticator;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.ResponseStatus;
import jp.ne.interspace.gurkha.model.UserSecret;
import jp.ne.interspace.gurkha.model.publisher.AuthenticateOauthCredentialsRequest;
import jp.ne.interspace.gurkha.model.publisher.PublisherAccountType;
import jp.ne.interspace.gurkha.model.publisher.PublisherStatusDetails;
import jp.ne.interspace.gurkha.service.internal.publisher.DefaultPublisherAccountActivationService;
import jp.ne.interspace.gurkha.service.internal.publisher.PublisherAuthenticationService;
import jp.ne.interspace.gurkha.service.publisher.PublisherAccountService;

import static jp.ne.interspace.gurkha.model.PublisherStatus.ACTIVATED;
import static jp.ne.interspace.gurkha.model.PublisherStatus.DENIED;
import static jp.ne.interspace.gurkha.model.PublisherStatus.NOT_ACTIVATED;
import static jp.ne.interspace.gurkha.model.publisher.PublisherAccountType.INFLUENCER;

/**
 * Controller for publisher authentication.
 *
 * <AUTHOR> OBS DEV Team
 */
public class PublisherAuthenticationController extends Controller {

    @Inject
    private RequestAuthenticator authenticator;

    @Inject
    private PublisherAuthenticationService publisherAuthenticationService;

    @Inject
    private PublisherAccountService publisherAccountService;

    @Inject
    private DefaultPublisherAccountActivationService publisherAccountActivationService;

    /**
     * Authenticates publisher credentials and sends user uid and secret in JSON response
     * if succeeded, or a 401 response if failed.
     * <p>
     * the authorization header must contain a SHA256-hashed string that composed of
     * user's credentials as described by the following pseudo code:
     * </p>
     * {@code SHA256(USERNAME + ':' + MD5(PASSWORD))}
     */
    public void authenticateCredentials() {
        UserSecret secret = authenticator
                .getPublisherSecretFrom(getRouteContext().getRequest());
        if (secret != null) {
            getRouteContext().json().send(secret);
        } else {
            throw new GurkhaApplicationException("Invalid publisher credential!",
                    ResponseStatus.UNAUTHORIZED);
        }
    }

    /**
     * Authenticates publisher credentials and sends user uid and secret in JSON response
     * if succeeded, or a 401 response if failed.
     * <p>
     * the authorization header must contain a SHA256-hashed string that composed of
     * user's credentials as described by the following pseudo code:
     * </p>
     * {@code SHA256(USERNAME + ':' + MD5(PASSWORD))}
     */
    public void influencerAuthenticateCredentials() {
        UserSecret secret = authenticator
                .getPublisherSecretFrom(getRouteContext().getRequest());
        if (secret != null) {
            PublisherAccountType type = publisherAccountService
                    .findPublisherAccountTypeBy(secret.getAccountId());
            if (type == INFLUENCER) {
                getRouteContext().json().send(secret);
            } else {
                throw new GurkhaApplicationException("Publisher type is not influencer.",
                        ResponseStatus.BAD_REQUEST);
            }
        } else {
            throw new GurkhaApplicationException("Invalid publisher credential!",
                    ResponseStatus.UNAUTHORIZED);
        }
    }

    /**
     * Authenticates Oauth credentials and sends user uid and secret in JSON response if
     * succeeded, or a 401 response if failed.
     *
     * if there is not Oauth credentials, create the one.
     */
    public void authenticateOauthCredentials() {
        AuthenticateOauthCredentialsRequest request = getRouteContext().getRequest()
                .createEntityFromBody(AuthenticateOauthCredentialsRequest.class);
        PublisherStatusDetails details = publisherAccountService
                .findStatusDetails(request.getEmail(), request.getCountryCode());
        if (details == null) {
            UserSecret secret = publisherAuthenticationService
                    .getSocialNetworkPublisherSecretFrom(request);
            oauthResponse(secret);
        } else if (details.getType() == INFLUENCER && details.getStatus() == ACTIVATED) {
            UserSecret secret = publisherAuthenticationService
                    .findSecretBy(details.getPublisherId());
            oauthResponse(secret);
        }  else if (details.getType() != INFLUENCER) {
            throw new GurkhaApplicationException("Publisher type is not influencer.",
                    ResponseStatus.NOT_ACCEPTABLE);
        } else if (details.getStatus() == NOT_ACTIVATED) {
            publisherAccountActivationService.activateAccountBy(details.getPublisherId());
            UserSecret secret = publisherAuthenticationService
                    .findSecretBy(details.getPublisherId());
            oauthResponse(secret);
        } else if (details.getStatus() == DENIED) {
            throw new GurkhaApplicationException(
                    "No account was found for this email address.",
                    ResponseStatus.NOT_FOUND);
        }
    }

    private void oauthResponse(UserSecret secret) {
        if (secret != null) {
            getRouteContext().json().send(secret);
        } else {
            throw new GurkhaApplicationException("Invalid social publisher credential!",
                    ResponseStatus.UNAUTHORIZED);
        }
    }
}
