/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.staff.StaffAccountService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for handling staff account.
 *
 * <AUTHOR>
 */
public class StaffAccountController extends Controller {

    @Inject
    private StaffAccountService staffAccountService;

    /**
     * Sends email by staff ID.
     */
    public void findStaffEmail() {
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text()
                .send(staffAccountService.findEmailBy(staff.getAccountId()));
    }
}
