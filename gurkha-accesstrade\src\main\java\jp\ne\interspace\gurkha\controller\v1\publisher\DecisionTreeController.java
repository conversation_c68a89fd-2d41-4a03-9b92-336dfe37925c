/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.DecisionTreeDataSearchRequest;
import jp.ne.interspace.gurkha.service.publisher.DecisionTreeService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling decision tree.
 *
 * <AUTHOR>
 */
public class DecisionTreeController extends Controller {

    @Inject
    private DecisionTreeService decisionTreeService;

    /**
     * Gets decision tree data.
     */
    public void findDecisionTreeData() {
        DecisionTreeDataSearchRequest request = getRouteContext()
                .createEntityFromParameters(DecisionTreeDataSearchRequest.class);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(decisionTreeService.findDecisionTreeData(request,
                publisherId.getAccountId()));
    }
}
