/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.util.Locale;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.ApplyAffiliationRequest;
import jp.ne.interspace.gurkha.service.publisher.AffiliationService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for campaign affiliation.
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON></a>
 */
public class AffiliationController extends Controller {

    private static final String SITE_ID_PARAMETER = "siteId";
    private static final String CAMPAIGN_ID_PARAMETER = "campaignId";

    @Inject
    private AffiliationService affiliationService;

    /**
     * Handles affiliate applications for campaigns.
     */
    public void applyForAffiliation() {
        ApplyAffiliationRequest request =
                getRouteContext().createEntityFromBody(ApplyAffiliationRequest.class);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        boolean isDynamoDbUpdateSuccessful = affiliationService
                .upsertAffiliateApplication(publisherId, request, locale, countryCode);

        if (isDynamoDbUpdateSuccessful) {
            getRouteContext().getResponse().ok();
        } else {
            getRouteContext().getResponse().accepted();
        }
    }

    /**
     * Sends true if given campaign and site is affiliated, otherwise false.
     */
    public void isAffiliated() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        getRouteContext().json().send(affiliationService.isAffiliated(campaignId, siteId,
                publisherId.getAccountId()));
    }
}
