/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.io.IOException;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.controller.transformer.IncomingFileTransformer;
import jp.ne.interspace.gurkha.model.OnboardingQuestionProgress;
import jp.ne.interspace.gurkha.model.OnboardingStatus;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.ChangePublisherPasswordRequest;
import jp.ne.interspace.gurkha.model.publisher.IncomingFile;
import jp.ne.interspace.gurkha.model.publisher.PublisherAccount;
import jp.ne.interspace.gurkha.model.publisher.PublisherAccountSummary;
import jp.ne.interspace.gurkha.model.publisher.PublisherOnboardingTaskProgress;
import jp.ne.interspace.gurkha.model.publisher.PublisherSocialNetworkRequest;
import jp.ne.interspace.gurkha.model.publisher.UpdateCorporatePublisherAccountRequest;
import jp.ne.interspace.gurkha.model.publisher.UpdateIndividualPublisherAccountRequest;
import jp.ne.interspace.gurkha.service.publisher.PublisherAccountService;
import jp.ne.interspace.gurkha.util.EncryptionUtils;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COLON;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.model.AwsSecretKeys.AES_256_GURKHA_IV;
import static jp.ne.interspace.gurkha.model.AwsSecretKeys.AES_256_GURKHA_SECRET_KEY;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.getSecretValues;

/**
 * Controller layer for managing the account detais of publishers.
 *
 * <AUTHOR> Varga
 */
public class PublisherAccountController extends Controller {

    private static final String ID_CARD_IMAGE_NAME = "idCardImage";
    private static final String NPWP_IMAGE_NAME = "npwpImage";
    private static final String PKP_IMAGE_NAME = "pkpImage";
    private static final String ACCOUNT_ID_PARAMETER = "accountId";
    private static final String UID_PARAMETER = "userUid";
    private static final String SECRET_KEY_PARAMETER = "secretKey";

    @Inject
    private PublisherAccountService publisherAccountService;

    @Inject
    private IncomingFileTransformer incomingFileTransformer;

    @Inject
    private EncryptionUtils encryptionUtils;

    /**
     * Sends the account details of the logged-in publisher to the client side.
     */
    public void findPublisherAccountDetails() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        PublisherAccount publisherAccount = publisherAccountService
                .findBy(publisherId.getAccountId());
        getRouteContext().json().send(publisherAccount);
    }

    /**
     * Sends the account summary of the logged-in publisher to the client side.
     */
    public void findPublisherAccountSummary() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        PublisherAccountSummary publisherAccountSummary = publisherAccountService
                .findSummaryBy(publisherId.getAccountId());
        getRouteContext().json().send(publisherAccountSummary);
    }

    /**
     * Changes the password of the logged-in publisher.
     */
    public void changePublisherPassword() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ChangePublisherPasswordRequest request = getRequest()
                .createEntityFromBody(ChangePublisherPasswordRequest.class);

        publisherAccountService.changePassword(publisherId, request);

        getResponse().ok();
    }

    /**
     * Updates the account details of the logged-in individual publisher.
     *
     * @throws IOException
     *             when cannot get the uploaded file contents
     */
    public void updateIndividualAccount() throws IOException {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        UpdateIndividualPublisherAccountRequest request = getRouteContext().getRequest()
                .createEntityFromBody(UpdateIndividualPublisherAccountRequest.class);

        IncomingFile idCardImage = incomingFileTransformer
                .getFileFromRequest(getRequest(), ID_CARD_IMAGE_NAME);

        IncomingFile npwpImage = incomingFileTransformer.getFileFromRequest(getRequest(),
                NPWP_IMAGE_NAME);
        publisherAccountService.updateIndividualAccount(publisherId, request, idCardImage,
                npwpImage);

        getRouteContext().getResponse().ok();
    }

    /**
     * Updates the account details of the logged-in corporate publisher.
     *
     * @throws IOException
     *             when cannot get the uploaded file contents
     */
    public void updateCorporateAccount() throws IOException {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        UpdateCorporatePublisherAccountRequest request = getRouteContext().getRequest()
                .createEntityFromBody(UpdateCorporatePublisherAccountRequest.class);

        IncomingFile npwpImage = incomingFileTransformer.getFileFromRequest(getRequest(),
                NPWP_IMAGE_NAME);
        IncomingFile pkpImage = incomingFileTransformer.getFileFromRequest(getRequest(),
                PKP_IMAGE_NAME);

        publisherAccountService.updateCorporateAccount(publisherId, request, npwpImage,
                pkpImage);

        getRouteContext().getResponse().ok();
    }

    /**
     * Checks if the publisher account is activated based on the given publisher.
     */
    public void isAccountActivated() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(publisherAccountService
                .isAccountActivated(publisherId.getAccountId()));
    }

    /**
     * Finds the onboarding status of the given publisher.
     */
    public void findOnboardingStatus() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(
                publisherAccountService.findOnboardingStatus(publisherId.getAccountId()));
    }

    /**
     * Updates the onboarding status of the given publisher.
     */
    public void updateOnboardingStatus() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        OnboardingStatus onboardingStatus = getRequest()
                .createEntityFromBody(OnboardingStatus.class);
        publisherAccountService.updateOnboardingStatus(publisherId, onboardingStatus);
        getRouteContext().getResponse().ok();
    }

    /**
     * Finds the email of the given publisher.
     */
    public void findEmail() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text()
                .send(publisherAccountService.findEmail(publisherId.getAccountId()));
    }

    /**
     * Sends onboarding task progress for publisher to the client side.
     */
    public void findOnboardingTaskProgress() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        PublisherOnboardingTaskProgress progress = publisherAccountService
                .findOnboardingTaskProgress(publisherId.getAccountId(), countryCode);
        getRouteContext().json().send(progress);
    }

    /**
     * Sends aes256 ecrypted key for influlencer to the client side.
     * @throws Exception
     *              when cannot encrypt secret key
     */
    public void generateSingleSignOnKey() throws Exception {
        String accountId = getRequest().getParameter(ACCOUNT_ID_PARAMETER).toString();
        String sercretKey = getRequest().getParameter(SECRET_KEY_PARAMETER).toString();
        String uid = getRequest().getParameter(UID_PARAMETER).toString();
        String secret = uid + COLON + sercretKey + COLON + accountId;
        getRouteContext().text()
                .send(encryptionUtils.generateAes256HashFrom(secret,
                        getSecretValues(AES_256_GURKHA_SECRET_KEY),
                        getSecretValues(AES_256_GURKHA_IV)));
    }

    /**
     * Sends the hashed publisher ID to the client side.
     *
     * @throws Exception
     *              when cannot encrypt publisher ID
     */
    public void findHashedPublisherId() throws Exception {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(encryptionUtils
                .generateAes256HashFrom(String.valueOf(publisherId.getAccountId()),
                        getSecretValues(AES_256_GURKHA_SECRET_KEY),
                        getSecretValues(AES_256_GURKHA_IV)));
    }

    /**
     * Checks if the publisher account is global publisher
     * based on the given publisher ID.
     */
    public void isGlobalPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(
                publisherAccountService.isGlobalPublisher(publisherId.getAccountId()));
    }

    /**
     * Checks if the publisher has enabled smart link tag feature.
     */
    public void isSmartLinkTagEnabled() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(publisherAccountService
                .isSmartLinkTagEnabled(publisherId.getAccountId()));
    }

    /**
     * Sends the currency of the publisher.
     */
    public void findCurrency() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text()
                .send(publisherAccountService.findCurrencyCodeBy(userId.getAccountId()));
    }

    /**
     * Updates the social network information of the publisher.
     */
    public void updateSocialNetwork() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PublisherSocialNetworkRequest request = getRouteContext().getRequest()
                .createEntityFromBody(PublisherSocialNetworkRequest.class);
        publisherAccountService.updateSocialNetwork(userId, request);
        getResponse().ok();
    }

    /**
     * Updates the onboarding question progress of the given publisher.
     */
    public void updateOnboardingQuestionProgress() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        OnboardingQuestionProgress onboardingQuestionProgress = getRequest()
                .createEntityFromBody(OnboardingQuestionProgress.class);
        publisherAccountService.updateOnboardingQuestionProgress(userId,
                onboardingQuestionProgress);
        getRouteContext().getResponse().ok();
    }

    /**
     * Finds the onboarding question progress of the given publisher.
     */
    public void findOnboardingQuestionProgress() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(publisherAccountService
                .findOnboardingQuestionProgressBy(userId.getAccountId()));
    }

    /**
     * Sends aes256 encrypted token for publisher to the client side.
     */
    public void generateSingleSignOnToken() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().text().send(publisherAccountService
                .generateSingleSignOnTokenBy(userId.getAccountId()));
    }

    /**
     * Checks if the bank account is valid based on the given publisher.
     */
    public void checkAccountSettings() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(publisherAccountService
                .checkAccountSettings(userId.getAccountId()));
    }

    /**
     * Checks if the bank account for influencer is valid based on the given publisher.
     */
    public void checkAccountSettingsForInfluencer() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(publisherAccountService
                .checkAccountSettingsForInfluencer(userId.getAccountId()));
    }
}
