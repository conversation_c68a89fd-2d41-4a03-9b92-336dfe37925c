/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.service.staff.ResultService;

/**
 * Controller for handling result target master.
 *
 * <AUTHOR>
 */
public class ResultTargetMasterController extends Controller {

    @Inject
    private ResultService resultService;

    /**
     * Sends all result target masters.
     */
    public void findAllResultTargetMasters() {
        getResponse().json().send(resultService.findAllResultTargetMasters());
    }
}
