/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.io.IOException;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.controller.transformer.IncomingFileTransformer;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.IncomingFile;
import jp.ne.interspace.gurkha.model.publisher.UpdateBankAccountRequest;
import jp.ne.interspace.gurkha.service.publisher.BankAccountService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling the bank account of a publisher.
 *
 * <AUTHOR>
 */
public class BankAccountController extends Controller {

    private static final String BANK_PASSBOOK_IMAGE_NAME = "bankPassbookImage";

    @Inject
    private BankAccountService bankAccountService;

    @Inject
    private IncomingFileTransformer incomingFileTransformer;

    /**
     * Sends the details of all the banks to the client.
     */
    public void findAllBanks() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().json().send(bankAccountService.findBanksBy(countryCode));
    }

    /**
     * Sends all bank account types to the client side.
     */
    public void findAllBankAccountTypes() {
        getRouteContext().json().send(bankAccountService.findAllBankAccountTypes());
    }

    /**
     * Sends the bank account of the publisher to the client side.
     */
    public void findBankAccountForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json()
                .send(bankAccountService.findBankAccountBy(publisherId.getAccountId()));
    }

    /**
     * Updates the bank account of the publisher based on the incoming request body.
     *
     * @throws IOException
     *             when cannot get image content
     */
    public void updateBankAccountForPublisher() throws IOException {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        UpdateBankAccountRequest request = getRouteContext().getRequest()
                .createEntityFromBody(UpdateBankAccountRequest.class);
        IncomingFile bankPassbookImage = incomingFileTransformer
                .getFileFromRequest(getRequest(), BANK_PASSBOOK_IMAGE_NAME);

        bankAccountService.update(publisherId, request, bankPassbookImage);

        getRouteContext().getResponse().ok();
    }
}
