/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.auth.v1;

import java.util.List;
import java.util.regex.Pattern;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.NonNull;

import ro.pippo.core.Request;
import ro.pippo.core.route.RouteContext;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.internal.staff.StaffPermissionService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COMMA;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.EMPTY;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.STAFF;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_TYPE;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.BIND_KEY_API_PERMISSIONS;
import static lombok.AccessLevel.PACKAGE;

/**
 * API Permission checker for the backend of ASEAN AccessTrade.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ApiPermissionChecker {

    @Inject @Named(BIND_KEY_API_PERMISSIONS) @VisibleForTesting @Getter(PACKAGE)
    private ImmutableMap<String, String> apiPermissions;

    @Inject
    private StaffPermissionService staffPermissionService;

    /**
     * Check and validate the permission by the API from {@link RouteContext}.
     *
     * @param routeContext
     *            {@link RouteContext} containing the HTTP request
     * @return {@code true} if API has no permission or account has permission by API,
     *      returns {@code false} if account has no permission by API
     */
    public boolean isValidApiPermission(@NonNull RouteContext routeContext) {
        boolean isValid = false;
        try {
            String[] permissions = findPermissionsBy(routeContext.getRequestUri());
            if (!isStaff(routeContext.getRequest())
                    || (permissions.length == 1 && EMPTY.equals(permissions[0]))) {
                isValid = true;
            } else {
                UserId userId = routeContext.getLocal(USER_ID_LOCAL_PARAMETER);
                List<String> permissionNames =
                        staffPermissionService.findPermissionNamesBy(
                                userId.getAccountId());
                for (String permission : permissions) {
                    if (permissionNames.contains(permission)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            // do nothing
        }
        return isValid;
    }

    @VisibleForTesting
    String[] findPermissionsBy(String uri) {
        return getApiPermissions().entrySet().stream()
                .filter(map -> Pattern.compile(map.getKey()).matcher(uri).find())
                .map(map -> map.getValue())
                .findFirst()
                .orElse(EMPTY)
                .split(COMMA);
    }

    private boolean isStaff(Request request) {
        return STAFF.equalsIgnoreCase(request.getHeader(USER_TYPE));
    }
}
