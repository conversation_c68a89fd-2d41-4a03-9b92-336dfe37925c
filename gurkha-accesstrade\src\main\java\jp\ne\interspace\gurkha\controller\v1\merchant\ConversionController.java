/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.merchant;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.merchant.ConversionRequest;
import jp.ne.interspace.gurkha.model.merchant.ConversionResultRequest;
import jp.ne.interspace.gurkha.model.merchant.CountConversionResultRequest;
import jp.ne.interspace.gurkha.service.merchant.ConversionService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for handling the conversions.
 *
 * <AUTHOR>
 */
public class ConversionController extends Controller {

    @Inject
    private ConversionService conversionService;

    /**
     * Sends the conversion for the given merchant.
     */
    public void findConversions() {
        UserId merchantId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ConversionRequest request = getRouteContext()
                .createEntityFromParameters(ConversionRequest.class);

        getRouteContext().json().send(
                conversionService.findConversionFor(merchantId.getAccountId(), request));
    }

    /**
     * Sends the conversion result for the given criteria.
     */
    public void findConversionResults() {
        ConversionResultRequest request = getRouteContext().createEntityFromParameters(
                ConversionResultRequest.class);
        getRouteContext().json().send(conversionService.findConversionResultFor(request));
    }

    /**
     * Sends the number of conversion result for the given criteria.
     */
    public void countConversionResult() {
        CountConversionResultRequest request = getRouteContext()
                .createEntityFromParameters(CountConversionResultRequest.class);

        getRouteContext().json()
                .send(conversionService.countConversionResultBy(request));
    }
}
