/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.util.List;

import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.model.NameValueResponse;
import jp.ne.interspace.gurkha.model.PagingRequest;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.AffiliatedCampaignSummary;
import jp.ne.interspace.gurkha.model.publisher.AppliedCampaignSummary;
import jp.ne.interspace.gurkha.model.publisher.CampaignDetails;
import jp.ne.interspace.gurkha.model.publisher.CampaignSummariesCount;
import jp.ne.interspace.gurkha.model.publisher.CampaignsAndCustomRules;
import jp.ne.interspace.gurkha.model.publisher.FindCampaignSummaryRequest;
import jp.ne.interspace.gurkha.model.publisher.FindCampaignsRequest;
import jp.ne.interspace.gurkha.model.publisher.HourlyRewardItem;
import jp.ne.interspace.gurkha.model.publisher.HourlyRewardRequest;
import jp.ne.interspace.gurkha.model.publisher.HourlyRewards;
import jp.ne.interspace.gurkha.model.publisher.LatestCampaign;
import jp.ne.interspace.gurkha.model.publisher.PromotedCampaign;
import jp.ne.interspace.gurkha.model.publisher.RejectedCampaignSummary;
import jp.ne.interspace.gurkha.model.publisher.SpecialCreativeCampaignSummary;
import jp.ne.interspace.gurkha.model.publisher.UnaffiliatedCampaignSummary;
import jp.ne.interspace.gurkha.model.publisher.UpsizedCampaign;
import jp.ne.interspace.gurkha.persist.hussar.model.publisher.CampaignAvailableForInfluencerItem;
import jp.ne.interspace.gurkha.service.publisher.CampaignService;
import jp.ne.interspace.gurkha.service.publisher.SiteService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling campaigns.
 *
 * author <a href="mailto:<EMAIL>">Jichoul Shin</a>
 */
@Slf4j
public class CampaignController extends PagingController {

    private static final String CAMPAIGN_ID_PARAMETER = "campaignId";
    private static final String SITE_ID_PARAMETER = "siteId";
    private static final String LIMIT_PARAMETER = "limit";
    private static final int LIMIT_DEFAULT = 100;

    @Inject
    private CampaignService campaignService;

    @Inject
    private SiteService siteService;

    /**
     * Sends all campaigns to the client.
     */
    public void findCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PagingRequest pagingRequest = getPagingRequest();
        FindCampaignsRequest findCampaignsRequest =
                getRouteContext().createEntityFromParameters(FindCampaignsRequest.class);
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        int total = campaignService.countCampaigns(findCampaignsRequest,
                publisherId.getAccountId(), publisherCountryCode);
        List<SpecialCreativeCampaignSummary> campaigns = campaignService
                .findCampaigns(findCampaignsRequest, pagingRequest,
                publisherId.getAccountId(), publisherCountryCode);
        sendJsonPaging(pagingRequest, campaigns, total);
    }

    /**
     * Sends the details of campaign by {@code campaignId} to the client.
     */
    public void findCampaignDetails() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        CampaignDetails details = campaignService.findDetailsBy(campaignId, siteId,
                publisherId.getAccountId(), publisherCountryCode);
        getRouteContext().json().send(details);
    }

    /**
     * Sends the affiliated campaigns to the client.
     */
    public void findAffiliatedCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        List<AffiliatedCampaignSummary> campaigns =
                campaignService.findAffiliatedCampaigns(siteId,
                        publisherId.getAccountId(), publisherCountryCode, request);

        getRouteContext().json().send(campaigns);
    }

    /**
     * Sends the applied campaigns to the client.
     */
    public void findAppliedCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext().createEntityFromParameters(
                FindCampaignSummaryRequest.class);
        List<AppliedCampaignSummary> campaigns = campaignService.findAppliedCampaigns(
                siteId, publisherId.getAccountId(), publisherCountryCode, request);
        getRouteContext().json().send(campaigns);
    }

    /**
     * Sends the rejected campaigns to the client.
     */
    public void findRejectedCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext().createEntityFromParameters(
                FindCampaignSummaryRequest.class);
        List<RejectedCampaignSummary> rejectedCampaigns =
                campaignService.findRejectedCampaigns(siteId, publisherId.getAccountId(),
                        publisherCountryCode, request);
        getRouteContext().json().send(rejectedCampaigns);
    }

    /**
     * Sends the unaffiliated campaigns to the client.
     */
    public void findUnaffiliatedCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PagingRequest pagingRequest = getPagingRequest();
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        List<UnaffiliatedCampaignSummary> campaigns = campaignService
                .findUnaffiliatedCampaigns(siteId, pagingRequest,
                        publisherId.getAccountId(), publisherCountryCode, request);
        getResponse().json().send(campaigns);
    }

    /**
     * Sends the latest unaffiliated campaigns for the publisher to the client side.
     */
    public void findLatestCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        List<LatestCampaign> response = campaignService
                .findLatestCampaignsFor(publisherId.getAccountId(), publisherCountryCode);

        getResponse().json().send(response);
    }

    /**
     * Sends the promoted campaigns for the publisher to the client side.
     */
    public void findPromotedCampaigns() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        List<PromotedCampaign> response = campaignService.findPromotedCampaignsFor(
                publisherId.getAccountId(), publisherCountryCode);

        getResponse().json().send(response);
    }

    /**
     * Sends the {@link NameValueResponse} items of the campaigns affiliated with the
     * given publisher to the client side.
     */
    public void findAffiliatedCampaignNamesAndIdsForPublisher() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        List<NameValueResponse> response = campaignService
                .findAffiliatedCampaignNamesAndIdsFor(siteId, publisherId.getAccountId());

        getResponse().json().send(response);
    }

    /**
     * Sends the {@link NameValueResponse} items of the affiliated and rejected
     * and prohinited campaigns with the given publisher to the client side.
     */
    public void findAffiliatedRejectedProhibitedCampaignNamesAndIdsForPublisher() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        List<NameValueResponse> response = campaignService
                .findAffiliatedRejectedProhibitedCampaignNamesAndIdsFor(siteId,
                        publisherId.getAccountId());

        getResponse().json().send(response);
    }

    /**
     * Sends the {@link CampaignAvailableForInfluencerItem} group of items of the
     * campaigns by category to the client side.
     */
    public void findCategoryCampaignAvailableForInfluencers() {
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        siteService.validatePublisherSite(siteId, userId.getAccountId());
        getResponse().json()
                .send(campaignService.findCategoryCampaignAvailableForInfluencers(siteId,
                        publisherCountryCode));
    }

    /**
     * Sends the {@link NameValueResponse} items of the campaigns running with the given
     * publisher country code.
     */
    public void findRunningCampaignNamesAndIds() {
        getResponse().json().send(campaignService.findRunningCampaignNamesAndIdsFor(
                getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER)));
    }

    /**
     * Sends the campaign status of given campaign ID.
     */
    public void findCampaignStatus() {
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        getResponse().json().send(campaignService.findCampaignStatusBy(campaignId));
    }

    /**
     * Sends the {@link NameValueResponse} items of the available promo campaigns with the
     * given publisher to the client side.
     */
    public void findAvailablePromosCampaignForPublisher() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json().send(campaignService
                .findAvailablePromosCampaign(siteId, publisherId.getAccountId(),
                        countryCode));
    }

    /**
     * Sends the fastest growing campaigns to the client.
     */
    public void findFastestGrowingCampaignSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        getResponse().json().send(campaignService.findFastestGrowingCampaignSummaries(
                publisherId.getAccountId(), siteId, publisherCountryCode, request));
    }

    /**
     * Sends the top campaigns to the client.
     */
    public void findTopCampaignSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        getResponse().json().send(campaignService.findTopCampaignSummaries(
                publisherId.getAccountId(), siteId, publisherCountryCode, request));
    }

    /**
     * Sends the paused campaigns to the client.
     */
    public void findPausedCampaignSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        getResponse().json().send(campaignService.findPausedCampaignSummariesBy(siteId,
                publisherId.getAccountId(), publisherCountryCode, request));
    }

    /**
     * Sends the terminated campaigns to the client.
     */
    public void findTerminatedCampaignSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        getResponse().json().send(campaignService.findTerminatedCampaignSummariesBy(
                siteId, publisherId.getAccountId(), publisherCountryCode, request));
    }

    /**
     * Sends the featured campaigns to the client.
     */
    public void findFeaturedCampaignSummaries() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        getResponse().json().send(campaignService.findFeaturedCampaignSummaries(siteId,
                publisherCountryCode, publisherId.getAccountId(), request));
    }

    /**
     * Sends the customer countries of all campaigns to the client.
     */
    public void findCustomerCountries() {
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json()
                .send(campaignService.findCustomerCountries(publisherCountryCode));
    }

    /**
     * Sends the campaign summaries count to the client.
     */
    public void countCampaignSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCampaignSummaryRequest request = getRouteContext()
                .createEntityFromParameters(FindCampaignSummaryRequest.class);
        CampaignSummariesCount campaignSummariesCount =
                campaignService.countCampaignSummaries(siteId,
                        publisherId.getAccountId(), publisherCountryCode, request);

        getRouteContext().json().send(campaignSummariesCount);
    }

    /**
     * Sends the brand bidding keywords of given campaign ID.
     */
    public void findBrandBiddingKeywords() {
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        getResponse().json().send(campaignService.findBrandBiddingKeywordsBy(campaignId));
    }

    /**
     * Sends the influencer campaign description by {@code campaignId}.
     */
    public void findInfluencerCampaignDescription() {
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        getRouteContext().text()
                .send(campaignService.findInfluencerCampaignDescription(campaignId));
    }

    /**
     * Sends the campaigns and custom rules to the client.
     */
    public void findCampaignsAndCustomRules() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        List<CampaignsAndCustomRules> response = campaignService
                .findCampaignsAndCustomRules(siteId, countryCode);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the upsized campaigns to the client.
     */
    public void findUpsizedCampaigns() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        int limit = getRouteContext().getParameter(LIMIT_PARAMETER).isEmpty()
                ? LIMIT_DEFAULT
                : getRouteContext().getParameter(LIMIT_PARAMETER).toInt();
        List<UpsizedCampaign> response = campaignService.findUpsizedCampaignsFor(siteId,
                publisherId.getAccountId(), publisherCountryCode, limit);
        getRouteContext().json().send(response);
    }

    /**
     * Sends hourly rewards for a campaign within a date range to the client.
     */
    public void findHourlyRewards() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        HourlyRewardRequest hourlyRewardRequest =
                getRouteContext().createEntityFromParameters(HourlyRewardRequest.class);

        List<HourlyRewardItem> hourlyRewards = campaignService.findHourlyRewards(
                campaignId, hourlyRewardRequest, publisherId.getAccountId());

        HourlyRewards response = new HourlyRewards(hourlyRewards);
        getRouteContext().json().send(response);
    }
}
