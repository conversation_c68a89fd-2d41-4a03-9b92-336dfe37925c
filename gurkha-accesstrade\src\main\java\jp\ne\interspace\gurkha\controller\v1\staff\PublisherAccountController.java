/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.List;

import com.google.inject.Inject;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.model.NameValueResponse;
import jp.ne.interspace.gurkha.model.publisher.PublisherPagingRequest;
import jp.ne.interspace.gurkha.service.staff.PublisherAccountService;

import static org.apache.commons.lang3.StringUtils.isNumeric;

/**
 * Controller layer for managing the account details of publishers.
 *
 * <AUTHOR>
 */
public class PublisherAccountController extends PagingController {

    private static final String SEARCH_KEYWORD_PARAMETER = "searchKeyword";

    @Inject
    private PublisherAccountService publisherAccountService;

    /**
     * Sends the publisher names and IDs for the given criteria to the client side.
     */
    public void findPublisherNamesAndIds() {
        String searchKeyword = getRouteContext().getParameter(SEARCH_KEYWORD_PARAMETER)
                .toString();
        PublisherPagingRequest pagingRequest = getPublisherPagingRequest();
        long publisherId = isNumeric(searchKeyword) ? Long.parseLong(searchKeyword) : 0;
        List<NameValueResponse> response = publisherAccountService
                .findPublisherNamesAndIdsBy(searchKeyword, publisherId,
                        pagingRequest);

        getResponse().json().send(response);
    }
}
