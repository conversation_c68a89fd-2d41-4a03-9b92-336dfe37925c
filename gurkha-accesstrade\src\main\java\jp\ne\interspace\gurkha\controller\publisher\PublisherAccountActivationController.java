/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.publisher;

import java.util.Locale;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.publisher.ActivatedAccount;
import jp.ne.interspace.gurkha.model.publisher.PublisherAccountActivationRequest;
import jp.ne.interspace.gurkha.model.publisher.PublisherResendActivationLinkRequest;
import jp.ne.interspace.gurkha.service.publisher.PublisherAccountActivationService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;

/**
 * Controller for handling pulisher account activation.
 *
 * <AUTHOR>
 */
public class PublisherAccountActivationController extends Controller {

    @Inject
    private PublisherAccountActivationService accountActivationService;

    /**
     * Activates a publisher's account based on the activation code in the incoming
     * request and sends activated account to client side.
     */
    public void activatePublisherAccount() {
        PublisherAccountActivationRequest request = getRequest()
                .createEntityFromBody(PublisherAccountActivationRequest.class);
        ActivatedAccount response = accountActivationService.activateAccountBy(request);
        getResponse().json().send(response);
    }

    /**
     * Resend activation link based on the email in the incoming request.
     */
    public void resendActivationLink() {
        PublisherResendActivationLinkRequest request = getRequest()
                .createEntityFromBody(PublisherResendActivationLinkRequest.class);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);
        accountActivationService.resendActivationLinkFor(request, locale);
        getResponse().ok();
    }
}
