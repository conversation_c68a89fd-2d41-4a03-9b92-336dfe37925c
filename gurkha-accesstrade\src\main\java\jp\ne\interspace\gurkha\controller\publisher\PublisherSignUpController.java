/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.publisher;

import java.util.Locale;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.publisher.CreateCorporatePublisherAccountWithSiteRequest;
import jp.ne.interspace.gurkha.model.publisher.CreateIndividualPublisherAccountWithSiteRequest;
import jp.ne.interspace.gurkha.service.publisher.PublisherSignUpService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;

/**
 * Controller for publisher sign up.
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jichoul Shin</a>
 */
public class PublisherSignUpController extends Controller {

    @Inject
    private PublisherSignUpService publisherSignUpService;

    /**
     * Creates a new individual publisher account along with its website.
     */
    public void createIndividualAccountWithSite() {
        CreateIndividualPublisherAccountWithSiteRequest request =
                getRouteContext().createEntityFromBody(
                        CreateIndividualPublisherAccountWithSiteRequest.class);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);

        publisherSignUpService.createIndividualAccountWithSite(request, locale);

        getRouteContext().getResponse().ok();
    }

    /**
     * Creates a new corporate publisher account along with its website.
     */
    public void createCorporateAccountWithSite() {
        CreateCorporatePublisherAccountWithSiteRequest request =
                getRouteContext().createEntityFromBody(
                        CreateCorporatePublisherAccountWithSiteRequest.class);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);

        publisherSignUpService.createCorporateAccountWithSite(request, locale);

        getRouteContext().getResponse().ok();
    }
}
