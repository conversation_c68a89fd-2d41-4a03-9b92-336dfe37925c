/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.List;

import com.google.inject.Inject;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.model.NameValueResponse;
import jp.ne.interspace.gurkha.model.publisher.PublisherPagingRequest;
import jp.ne.interspace.gurkha.service.staff.SiteService;

import static org.apache.commons.lang3.StringUtils.isNumeric;

/**
 * Controller class for operations on publisher sites.
 *
 * <AUTHOR>
 */
public class SiteController extends PagingController {

    private static final String SEARCH_KEYWORD_PARAMETER = "searchKeyword";
    private static final String PATH_PARAM_SITE_ID = "siteId";

    @Inject
    private SiteService siteService;

    /**
     * Sends the site names and IDs for the given criteria to the client side.
     */
    public void findSiteNamesAndIds() {
        String searchKeyword = getRouteContext().getParameter(SEARCH_KEYWORD_PARAMETER)
                .toString();
        PublisherPagingRequest pagingRequest = getPublisherPagingRequest();
        long siteId = isNumeric(searchKeyword) ? Long.parseLong(searchKeyword) : 0;
        List<NameValueResponse> response = siteService
                .findSiteNamesAndIdsBy(searchKeyword, siteId, pagingRequest);

        getResponse().json().send(response);
    }

    /**
     * Sends the site details for the given criteria to the client side.
     */
    public void findSiteDetails() {
        Long siteId = getRouteContext().getParameter(PATH_PARAM_SITE_ID).toLong();
        getResponse().json().send(siteService.findSiteDetails(siteId));
    }
}
