/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.CountProductAndCreativeRequest;
import jp.ne.interspace.gurkha.model.publisher.ProductSummariesSearchRequest;
import jp.ne.interspace.gurkha.service.publisher.ProductService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;
/**
 * Controller for handling product detail.
 *
 * <AUTHOR> Tran
 */
public class ProductController extends Controller {

    private static final String PRODUCT_ID_PARAMETER = "productId";
    private static final String SITE_ID_PARAMETER = "siteId";

    @Inject
    private ProductService productService;

    /**
     * Sends the product details for influencer.
     */
    public void findProductDetails() {
        String productId = getRouteContext().getParameter(PRODUCT_ID_PARAMETER)
                .toString();
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().json().send(productService.findProductDetails(productId,
                userId.getAccountId(), siteId, countryCode));
    }

    /**
     * Sends the product summaries for influencer.
     */
    public void findProductSummaries() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        ProductSummariesSearchRequest request = getRouteContext()
                .createEntityFromParameters(ProductSummariesSearchRequest.class);
        getRouteContext().json().send(
                productService.findProductSummaries(userId.getAccountId(), countryCode,
                        request));
    }

    /**
     * Sends the product and creative total for influencer.
     */
    public void count() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        CountProductAndCreativeRequest request = getRouteContext()
                .createEntityFromParameters(CountProductAndCreativeRequest.class);
        getRouteContext().json().send(productService.countProductAndCreative(
                userId.getAccountId(), countryCode, request));
    }
}
