/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.model.RewardPagingRequest;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.staff.FindDefaultRewardPriceSettingsRequest;
import jp.ne.interspace.gurkha.model.staff.InsertHourlyDefaultRewardPriceSettingRequest;
import jp.ne.interspace.gurkha.model.staff.InsertMonthlyDefaultRewardPriceSettingRequest;
import jp.ne.interspace.gurkha.service.staff.RewardSettingService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling monthly/hourly reward setting.
 *
 * <AUTHOR> Tran
 */
public class RewardSettingController extends PagingController {

    @Inject
    private RewardSettingService rewardSettingService;

    /**
     * Sends the monthly/hourly default reward price settings for the given criteria to
     * the client side.
     */
    public void findDefaultRewardPriceSettings() {
        FindDefaultRewardPriceSettingsRequest request = getRouteContext()
                .createEntityFromParameters(FindDefaultRewardPriceSettingsRequest.class);
        RewardPagingRequest pagingRequest = getRewardPagingRequest();
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json().send(rewardSettingService
                .findDefaultRewardPriceSettings(
                        request, pagingRequest, staff.getAccountId(), staffCountryCode));
    }

    /**
     * Inserts the monthly default reward price setting for the given request.
     */
    public void insertMonthlyDefaultRewardPriceSetting() {
        InsertMonthlyDefaultRewardPriceSettingRequest request = getRouteContext()
                .createEntityFromBody(
                        InsertMonthlyDefaultRewardPriceSettingRequest.class);
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json().send(
                rewardSettingService.insertDefaultRewardPriceSetting(request,
                        staff.getAccountId(), staffCountryCode));
    }

    /**
     * Inserts the hourly default reward price setting for the given request.
     */
    public void insertHourlyDefaultRewardPriceSetting() {
        InsertHourlyDefaultRewardPriceSettingRequest request = getRouteContext()
                .createEntityFromBody(InsertHourlyDefaultRewardPriceSettingRequest.class);
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json().send(
                rewardSettingService.insertHourlyDefaultRewardPriceSetting(request,
                        staff.getAccountId(), staffCountryCode));
    }
}
