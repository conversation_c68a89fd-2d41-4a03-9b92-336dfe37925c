/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.List;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.NameValueResponse;
import jp.ne.interspace.gurkha.service.staff.MerchantAccountService;

import static org.apache.commons.lang3.StringUtils.isNumeric;

/**
 * Controller layer for handling the merchant accounts.
 *
 * <AUTHOR>
 */
public class MerchantAccountController extends Controller {

    private static final String SEARCH_KEYWORD_PARAMETER = "searchKeyword";

    @Inject
    private MerchantAccountService merchantAccountService;

    /**
     * Sends the merchant names and IDs for the given criteria to the client side.
     */
    public void findMerchantNamesAndIds() {
        String searchKeyword = getRouteContext().getParameter(SEARCH_KEYWORD_PARAMETER)
                .toString();
        long merchantId = isNumeric(searchKeyword) ? Long.parseLong(searchKeyword) : 0;
        List<NameValueResponse> response = merchantAccountService
                .findMerchantNamesAndIdsBy(searchKeyword, merchantId);

        getResponse().json().send(response);
    }
}
