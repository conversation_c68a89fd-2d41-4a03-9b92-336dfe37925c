/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.service.publisher.ProductService;

/**
 * Controller for handling product.
 *
 * <AUTHOR>
 */
public class ProductController extends Controller {

    private static final String SUBDOMAIN_PARAMETER = "subdomain";
    private static final String PRODUCT_ID_PARAMETER = "productId";

    @Inject
    private ProductService productService;

    /**
     * Sends product data.
     */
    public void findProduct() {
        String subdomain = getRouteContext().getParameter(SUBDOMAIN_PARAMETER).toString();
        String productId = getRouteContext().getParameter(PRODUCT_ID_PARAMETER)
                .toString();
        getResponse()
                .json(productService.findProductForInfluencerSite(subdomain, productId));
    }
}
