/**
 * Copyright © 2022 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.FindPaymentHistoryRequest;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.staff.CancelPaymentInvoiceRequest;
import jp.ne.interspace.gurkha.model.staff.ConfirmPaymentInvoiceRequest;
import jp.ne.interspace.gurkha.model.staff.ConfirmPaymentInvoicesRequest;
import jp.ne.interspace.gurkha.model.staff.PaymentInvoiceRequest;
import jp.ne.interspace.gurkha.service.staff.PaymentService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for payment.
 *
 * <AUTHOR> Shin
 */
@Slf4j
public class PaymentController extends Controller {

    private static final String INVOICE_ID_PARAMETER = "invoiceId";

    @Inject
    private PaymentService paymentService;

    /**
     * Inserts payment invoice data.
     */
    public void insertPaymentInvoice() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PaymentInvoiceRequest request = getRouteContext()
                .createEntityFromBody(PaymentInvoiceRequest.class);
        getLogger().info("CREATE INVOICE BY REQUEST:" + getRequest().getBody());
        getResponse().json(
                paymentService.insertPaymentInvoice(request, userId.getAccountId()));
    }

    /**
     * Cancels payment invoice data.
     */
    public void cancelPaymentInvoice() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        CancelPaymentInvoiceRequest request = getRouteContext()
                .createEntityFromBody(CancelPaymentInvoiceRequest.class);
        getLogger().info("CANCEL INVOICE BY REQUEST:" + getRequest().getBody());
        paymentService.cancelPaymentInvoice(request, userId.getAccountId());
        getRouteContext().getResponse().ok();
    }

    /**
     * Confirms payment invoice data.
     */
    public void confirmPaymentInvoice() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ConfirmPaymentInvoiceRequest request = getRouteContext()
                .createEntityFromBody(ConfirmPaymentInvoiceRequest.class);
        getLogger().info("CONFIRM INVOICE BY REQUEST:" + getRequest().getBody());
        paymentService.confirmPaymentInvoice(request, userId.getAccountId());
        getRouteContext().getResponse().ok();
    }

    /**
     * Confirms payment invoice data.
     */
    public void confirmPaymentInvoices() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ConfirmPaymentInvoicesRequest request = getRouteContext()
                .createEntityFromBody(ConfirmPaymentInvoicesRequest.class);
        getLogger().info("CONFIRM INVOICES BY REQUEST:" + getRequest().getBody());
        paymentService.confirmPaymentInvoices(request, userId.getAccountId());
        getRouteContext().getResponse().ok();
    }

    /**
     * Sends the payment details for the given criteria to the client side.
     */
    public void findPaymentDetails() {
        String invoiceId = getRouteContext()
                .getParameter(INVOICE_ID_PARAMETER).toString();
        getRouteContext().json().send(paymentService.findPaymentDetailsBy(invoiceId));
    }

    /**
     * Sends the payment history for the given criteria to the client side.
     */
    public void findPaymentHistory() {
        FindPaymentHistoryRequest request = getRouteContext()
                .createEntityFromBody(FindPaymentHistoryRequest.class);
        getRouteContext().json().send(paymentService.findPaymentHistory(request));
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
