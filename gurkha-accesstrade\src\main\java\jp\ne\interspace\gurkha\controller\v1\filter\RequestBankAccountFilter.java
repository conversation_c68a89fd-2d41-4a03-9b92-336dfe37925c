/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.filter;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.auth.v1.ApiOtpChecker;

import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.ResponseStatus;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.OPT_TOKEN;

/**
 * Before filters for API requests.
 *
 * <AUTHOR>
 */
public class RequestBankAccountFilter extends Controller {

    @Inject
    private ApiOtpChecker apiOtpChecker;

    /**
     * Validates otp token by API and sends a 401 response if failed.
     */
    public void validateOtpTokenRequest() {
        String otpToken = getRouteContext().getHeader(OPT_TOKEN);
        if (apiOtpChecker.isValidOtpToken(otpToken)) {
            proceedToTheNextHandler();
        } else {
            throw new GurkhaApplicationException(
                    String.format("OTP token verification failed | Authorization: %s",
                            otpToken),
                    ResponseStatus.UNAUTHORIZED);
        }
    }

    private void proceedToTheNextHandler() {
        getRouteContext().next();
    }
}
