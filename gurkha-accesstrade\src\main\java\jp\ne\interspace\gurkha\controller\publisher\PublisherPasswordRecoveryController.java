/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.publisher;

import java.util.Locale;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.publisher.PublisherPasswordRecoveryRequest;
import jp.ne.interspace.gurkha.model.publisher.PublisherResetPasswordRequest;
import jp.ne.interspace.gurkha.service.publisher.PublisherPasswordRecoveryService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;

/**
 * Controller for handling requests related to password recovery for publishers.
 *
 * <AUTHOR>
 */
public class PublisherPasswordRecoveryController extends Controller {

    @Inject
    private PublisherPasswordRecoveryService passwordRecoveryService;

    /**
     * Initiates the password recovery process for the given publisher.
     */
    public void initiatePasswordRecovery() {
        PublisherPasswordRecoveryRequest request = getRequest()
                .createEntityFromBody(PublisherPasswordRecoveryRequest.class);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);
        passwordRecoveryService.initiatePasswordRecoveryFor(request, locale);
        getResponse().ok();
    }

    /**
     * Resets the password of the given publisher.
     */
    public void resetPassword() {
        PublisherResetPasswordRequest request = getRequest()
                .createEntityFromBody(PublisherResetPasswordRequest.class);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);
        passwordRecoveryService.resetPasswordFor(request, locale);
        getResponse().ok();
    }
}
