/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.auth.v1;

import java.time.Instant;
import java.util.Date;

import javax.annotation.Nullable;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.JsonObject;
import com.google.inject.Inject;

import io.jsonwebtoken.Claims;

import lombok.NonNull;

import ro.pippo.core.Request;
import ro.pippo.core.route.RouteContext;

import jp.ne.interspace.gurkha.auth.jwt.JwtProcessor;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.UserSecret;
import jp.ne.interspace.gurkha.service.AuthenticationService;
import jp.ne.interspace.gurkha.service.internal.merchant.MerchantAuthenticationService;
import jp.ne.interspace.gurkha.service.internal.publisher.PublisherAuthenticationService;
import jp.ne.interspace.gurkha.service.internal.staff.StaffAuthenticationService;
import jp.ne.interspace.gurkha.service.publisher.PublisherAccountService;
import jp.ne.interspace.gurkha.service.staff.StaffAccountService;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.concurrent.TimeUnit.MINUTES;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;
import static jp.ne.interspace.gurkha.config.GurkhaConfig.DEFAULT_JWT_IAT_DIFFERENCE_ACCEPTABLE_RANGE_IN_SEC;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.BEARER_ENDED_WITH_SPACE;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.IS_GLOBAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.MERCHANT;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.ONE_SEC_IN_MILLIS;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.PUBLISHER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.STAFF;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USERNAME;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Authentication helper for the backend of ASEAN AccessTrade.
 *
 * <AUTHOR> OBS DEV Team
 */
@Singleton
public class RequestAuthenticator {

    private static final String USER_TYPE_LOCAL_PARAMETER = "userType";

    @Inject
    private JwtProcessor jwtProcessor;

    @Inject
    private PublisherAuthenticationService publisherAuthenticationService;

    @Inject
    private MerchantAuthenticationService merchantAuthenticationService;

    @Inject
    private StaffAuthenticationService staffAuthenticationService;

    @Inject
    private PublisherAccountService publisherAccountService;

    @Inject
    private StaffAccountService staffAccountService;

    private LoadingCache<Long, String> publisherCountryCodeCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000).expireAfterWrite(5, MINUTES)
            .build(new CacheLoader<Long, String>() {

                @Override
                public String load(Long accountId) throws Exception {
                    return publisherAccountService.findCountryCodeBy(accountId);
                }
            });

    private LoadingCache<Long, String> staffCountryCodeCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000).expireAfterWrite(5, MINUTES)
            .build(new CacheLoader<Long, String>() {

                @Override
                public String load(Long accountId) throws Exception {
                    return staffAccountService.findCountryCodeBy(accountId);
                }
            });

    /**
     * Check and validate the publisher's credentials on the header of the given
     * {@link Request}, then return the corresponding user secret.
     *
     * the authorization header must contain a SHA256-hashed string that
     * composed of user's credentials as described by the following pseudo code:
     *
     * {@code SHA256(USERNAME + ':' + MD5(PASSWORD))}
     *
     *
     * @param request
     *            HTTP request to validate and authenticate
     * @return {@link UserSecret} corresponding to the given credentials or
     *         {@code null} if no match found
     */
    public @Nullable UserSecret getPublisherSecretFrom(Request request) {
        return publisherAuthenticationService.findSecretBy(
                request.getParameter(COUNTRY_CODE_LOCAL_PARAMETER).toString(),
                request.getParameter(IS_GLOBAL_PARAMETER).toBoolean(),
                request.getParameter(USERNAME).toString(),
                request.getHeader(AUTHORIZATION));
    }

    /**
     * Check and validate the merchant's credentials on the header of the given
     * {@link Request}, then return the corresponding user secret.
     *
     * the authorization header must contain a SHA256-hashed string that
     * composed of user's credentials as described by the following pseudo code:
     *
     * {@code SHA256(USERNAME + ':' + MD5(PASSWORD))}
     *
     *
     * @param request
     *            HTTP request to validate and authenticate
     * @return {@link UserSecret} corresponding to the given credentials or
     *         {@code null} if no match found
     */
    public @Nullable UserSecret getMerchantSecretFrom(Request request) {
        return merchantAuthenticationService.findSecretBy(
                request.getParameter(USERNAME).toString(),
                request.getHeader(AUTHORIZATION));
    }

    /**
     * Check and validate the staff's credentials on the header of the given
     * {@link Request}, then return the corresponding user secret.
     *
     * the authorization header must contain a SHA256-hashed string that
     * composed of user's credentials as described by the following pseudo code:
     *
     * {@code SHA256(USERNAME + ':' + MD5(PASSWORD))}
     *
     *
     * @param request
     *            HTTP request to validate and authenticate
     * @return {@link UserSecret} corresponding to the given credentials or
     *         {@code null} if no match found
     */
    public @Nullable UserSecret getStaffSecretFrom(Request request) {
        return staffAuthenticationService.findSecretBy(
                request.getParameter(USERNAME).toString(),
                request.getHeader(AUTHORIZATION));
    }

    /**
     * Check and validate the bearer JWT on the header of the given
     * {@link Request}.
     *
     * @param routeContext
     *            {@link RouteContext} containing the HTTP request to validate
     *            and authenticate
     * @return {@code false} if failed to authenticate otherwise returns
     *         {@code true}
     */
    public boolean isValidJwtOn(@NonNull RouteContext routeContext) {
        boolean isValid = false;
        try {
            String authorizationHeader = routeContext.getHeader(AUTHORIZATION);

            if (isWellFormed(authorizationHeader)) {

                String jwt = authorizationHeader
                        .substring(BEARER_ENDED_WITH_SPACE.length());
                JsonObject unsignedClaim = jwtProcessor.extractUnsignedClaimsFrom(jwt);
                UserSecret userSecret = findSecretBy(
                        jwtProcessor.getUnsignedSubFrom(unsignedClaim), routeContext);
                byte[] secret = userSecret.getSecretKey().getBytes(UTF_8);
                Claims claims = jwtProcessor.extractClaimsFrom(jwt, secret);
                isValid = validate(claims.getIssuedAt());

                if (isValid) {
                    if (isPublisher(routeContext) || isStaff(routeContext)) {
                        String countryCode = findUserCountryCode(routeContext,
                                userSecret.getAccountId());
                        routeContext.setLocal(COUNTRY_CODE_LOCAL_PARAMETER, countryCode);
                    }
                    routeContext.setLocal(USER_ID_LOCAL_PARAMETER, new UserId(
                            userSecret.getAccountId(), userSecret.getUserUid()));
                }
            }
        } catch (Exception ex) {
            // consume and do nothing to keep it quiet for PROD
            // (unless we decide to leave AuthHelper's logs
            // separately for future security analysis)
            // log.error("JWT auth failed due to:", ex);
        }
        return isValid;
    }

    @VisibleForTesting
    String findUserCountryCode(RouteContext routeContext, long accountId)
            throws Exception {
        if (isPublisher(routeContext)) {
            return publisherCountryCodeCache.get(accountId);
        }
        if (isStaff(routeContext)) {
            return staffCountryCodeCache.get(accountId);
        }

        throw new GurkhaApplicationException("Invalid user type");
    }

    @VisibleForTesting
    AuthenticationService getAuthenticationServiceBy(RouteContext routeContext) {
        AuthenticationService authenticationService = publisherAuthenticationService;
        if (isMerchant(routeContext)) {
            authenticationService = merchantAuthenticationService;
        } else if (isStaff(routeContext)) {
            authenticationService = staffAuthenticationService;
        }
        return authenticationService;
    }

    private boolean isMerchant(RouteContext routeContext) {
        return MERCHANT
                .equalsIgnoreCase(routeContext.getLocal(USER_TYPE_LOCAL_PARAMETER));
    }

    private boolean isStaff(RouteContext routeContext) {
        return STAFF.equalsIgnoreCase(routeContext.getLocal(USER_TYPE_LOCAL_PARAMETER));
    }

    private boolean isPublisher(RouteContext routeContext) {
        return PUBLISHER
                .equalsIgnoreCase(routeContext.getLocal(USER_TYPE_LOCAL_PARAMETER));
    }

    private UserSecret findSecretBy(String jwtSub, RouteContext routeContext) {
        UserSecret secret = getAuthenticationServiceBy(routeContext).findSecretBy(jwtSub);
        if (secret == null || Strings.isNullOrEmpty(secret.getSecretKey())) {
            throw new GurkhaApplicationException("No user secret found.");
        }
        return secret;
    }

    private boolean isWellFormed(String authorizationHeader) {
        return (!Strings.isNullOrEmpty(authorizationHeader)
                && authorizationHeader.startsWith(BEARER_ENDED_WITH_SPACE)
                && authorizationHeader.length() > BEARER_ENDED_WITH_SPACE.length());
    }

    private boolean validate(@Nullable Date jwtIat) {
        boolean isValid = false;
        if (jwtIat != null) {
            long jwtIatInSec = jwtIat.getTime() / ONE_SEC_IN_MILLIS;
            isValid = isTimeDifferenceAcceptable(jwtIatInSec);
        }
        return isValid;
    }

    private boolean isTimeDifferenceAcceptable(long jwtIatInSec) {
        long delta = Instant.now().getEpochSecond() - jwtIatInSec;
        if (delta < 0) {
            delta = delta * -1;
        }
        return delta <= DEFAULT_JWT_IAT_DIFFERENCE_ACCEPTABLE_RANGE_IN_SEC;
    }

}
