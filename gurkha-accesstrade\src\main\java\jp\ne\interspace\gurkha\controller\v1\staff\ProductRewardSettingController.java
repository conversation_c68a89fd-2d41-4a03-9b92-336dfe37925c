/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.model.RewardPagingRequest;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.staff.DeleteAllProductRewardSettingRequest;
import jp.ne.interspace.gurkha.model.staff.DeleteHourlyProductRewardSettingRequest;
import jp.ne.interspace.gurkha.model.staff.DeleteMonthlyProductRewardSettingRequest;
import jp.ne.interspace.gurkha.model.staff.FindProductRewardPriceSettingsRequest;
import jp.ne.interspace.gurkha.model.staff.InsertHourlyProductRewardPriceSettingsRequest;
import jp.ne.interspace.gurkha.model.staff.InsertMonthlyProductRewardPriceSettingRequest;
import jp.ne.interspace.gurkha.model.staff.ProductRewardSettingUpdateRequest;
import jp.ne.interspace.gurkha.model.staff.RewardPriceSettingResponse;
import jp.ne.interspace.gurkha.service.staff.HourlyProductRewardSettingService;
import jp.ne.interspace.gurkha.service.staff.ProductRewardSettingService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling monthly and hourly product reward setting.
 *
 * <AUTHOR> Vuong
 */
public class ProductRewardSettingController extends PagingController {

    @Inject
    private ProductRewardSettingService productRewardSettingService;

    @Inject
    private HourlyProductRewardSettingService hourlyProductRewardSettingService;

    /**
     * Deletes monthly product reward settings.
     */
    public void deleteMonthlyProductRewardSettings() {
        DeleteMonthlyProductRewardSettingRequest request = getRouteContext()
                .createEntityFromBody(DeleteMonthlyProductRewardSettingRequest.class);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(productRewardSettingService
                .deleteMonthlyProductRewardSetting(request, userId.getAccountId()));
    }

    /**
     * Deletes all monthly product reward settings.
     */
    public void deleteAllMonthlyProductRewardSettings() {
        DeleteAllProductRewardSettingRequest request = getRouteContext()
                .createEntityFromBody(DeleteAllProductRewardSettingRequest.class);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(productRewardSettingService
                .deleteAllMonthlyProductRewardSetting(request, userId.getAccountId()));
    }

    /**
     * Deletes hourly product reward settings.
     */
    public void deleteHourlyProductRewardSettings() {
        DeleteHourlyProductRewardSettingRequest request = getRouteContext()
                .createEntityFromBody(DeleteHourlyProductRewardSettingRequest.class);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(hourlyProductRewardSettingService
                .deleteHourlyProductRewardSetting(request, userId.getAccountId()));
    }

    /**
     * Deletes all hourly product reward settings.
     */
    public void deleteAllHourlyProductRewardSettings() {
        DeleteAllProductRewardSettingRequest request = getRouteContext()
                .createEntityFromBody(DeleteAllProductRewardSettingRequest.class);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(hourlyProductRewardSettingService
                .deleteAllHourlyProductRewardSetting(request, userId.getAccountId()));
    }

    /**
     * Sends the monthly/hourly product reward price settings for the given criteria to
     * the client side.
     */
    public void findProductRewardPriceSettings() {
        FindProductRewardPriceSettingsRequest request = getRouteContext()
                .createEntityFromParameters(FindProductRewardPriceSettingsRequest.class);
        RewardPagingRequest pagingRequest = getRewardPagingRequest();
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json().send(productRewardSettingService
                .findProductRewardPriceSettings(request, pagingRequest,
                        staff.getAccountId(), staffCountryCode));
    }

    /**
     * Inserts the monthly product reward price settings for the given request.
     */
    public void insertMonthlyProductRewardPriceSettings() {
        InsertMonthlyProductRewardPriceSettingRequest request = getRouteContext()
                .createEntityFromBody(
                        InsertMonthlyProductRewardPriceSettingRequest.class);
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        RewardPriceSettingResponse response = productRewardSettingService
                .insertMonthlyProductRewardPriceSettings(request, staff.getAccountId(),
                        staffCountryCode);
        getRouteContext().json().send(response);
    }

    /**
     * Inserts the hourly product reward price settings for the given request.
     */
    public void insertHourlyProductRewardPriceSettings() {
        InsertHourlyProductRewardPriceSettingsRequest request
                = getRouteContext().createEntityFromBody(
                InsertHourlyProductRewardPriceSettingsRequest.class);
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        RewardPriceSettingResponse response = hourlyProductRewardSettingService
                .insertHourlyProductRewardPriceSetting(
                        request, staff.getAccountId(), staffCountryCode);
        getResponse().json().send(response);
    }

    /**
     * Update product reward setting.
     */
    public void updateProductRewardSetting() {
        ProductRewardSettingUpdateRequest request = getRouteContext()
                .createEntityFromBody(ProductRewardSettingUpdateRequest.class);
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String staffCountryCode = getRouteContext().getLocal(
                COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().json().send(productRewardSettingService
                .updateProductRewardSetting(request,
                        staff.getAccountId(), staffCountryCode));
    }
}
