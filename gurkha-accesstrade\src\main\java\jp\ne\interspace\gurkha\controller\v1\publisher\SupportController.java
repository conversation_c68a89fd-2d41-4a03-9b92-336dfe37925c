/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.util.Locale;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.SupportRequest;
import jp.ne.interspace.gurkha.service.publisher.SupportService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for the Support APIs.
 *
 * <AUTHOR>
 */
public class SupportController extends Controller {

    @Inject
    private SupportService supportService;

    /**
     * Sends the support page inquiry email for the given criteria.
     */
    public void sendSupportEmail() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        SupportRequest request = getRouteContext().getRequest()
                .createEntityFromBody(SupportRequest.class);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        supportService.sendInquiry(request, publisherId, locale, countryCode);

        getRouteContext().getResponse().ok();
    }
}
