/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.util.List;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.InsertViewedNotificationsRequest;
import jp.ne.interspace.gurkha.model.publisher.Notification;
import jp.ne.interspace.gurkha.service.publisher.NotificationService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for handling notifications.
 *
 * <AUTHOR>
 */
public class NotificationController extends Controller {

    private static final boolean IS_INFLUENCER = true;

    @Inject
    private NotificationService notificationService;

    /**
     * Sends the notifications by country code aimed at publishers to the client side.
     */
    public void findNotificationsForPublishers() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        List<Notification> response = notificationService.findNotifications(countryCode,
                publisherId.getAccountId(), !IS_INFLUENCER);
        getResponse().json().send(response);
    }

    /**
     * Sends the notifications by country code aimed at influencer to the client side.
     */
    public void findNotificationsForInfluencers() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        List<Notification> response = notificationService.findNotifications(countryCode,
                userId.getAccountId(), IS_INFLUENCER);
        getResponse().json().send(response);
    }

    /**
     * Inserts viewed notifications for publisher.
     */
    public void insertViewedNotifications() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        InsertViewedNotificationsRequest request = getRouteContext()
                .createEntityFromBody(InsertViewedNotificationsRequest.class);
        getResponse().json().send(notificationService.insertViewedNotifications(
                request.getNotificationIds(), publisherId.getAccountId()));
    }
}
