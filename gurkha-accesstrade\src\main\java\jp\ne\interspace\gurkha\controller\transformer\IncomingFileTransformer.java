/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.transformer;

import java.io.IOException;

import javax.inject.Singleton;

import ro.pippo.core.FileItem;
import ro.pippo.core.Request;

import jp.ne.interspace.gurkha.model.publisher.IncomingFile;

/**
 * IncomingFile Transformer.
 *
 * <AUTHOR>
 */
@Singleton
public class IncomingFileTransformer {

    /**
     * Transform {@link FileItem} to {@link IncomingFile}.
     *
     * @param request
     *            request from client
     * @param bodyPartName
     *            the part of request body containing the file contents
     * @return Return IncomingFile or null
     * @throws IOException
     *             throw exception when an error occurs
     */
    public IncomingFile getFileFromRequest(Request request, String bodyPartName)
            throws IOException {
        FileItem fileInRequest = request.getFile(bodyPartName);
        return fileInRequest != null
                ? new IncomingFile(fileInRequest.getSubmittedFileName(),
                        fileInRequest.getInputStream())
                : null;
    }
}
