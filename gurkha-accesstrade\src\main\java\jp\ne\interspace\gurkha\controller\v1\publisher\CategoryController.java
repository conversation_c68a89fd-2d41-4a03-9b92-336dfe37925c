/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.service.publisher.CategoryService;

/**
 * Controller layer for handling the categories.
 *
 * <AUTHOR>
 */
public class CategoryController extends Controller {

    @Inject
    private CategoryService categoryService;

    /**
     * Sends all categories to the client side.
     */
    public void findAll() {
        getResponse().json().send(categoryService.findAll());
    }
}
