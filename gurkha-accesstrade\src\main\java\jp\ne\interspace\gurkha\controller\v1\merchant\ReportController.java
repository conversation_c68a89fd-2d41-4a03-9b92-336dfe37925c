/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.merchant;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.merchant.DailyReportRequest;
import jp.ne.interspace.gurkha.model.merchant.MonthlyReportRequest;
import jp.ne.interspace.gurkha.model.merchant.PerformanceReportRequest;
import jp.ne.interspace.gurkha.service.merchant.DateBasedReportService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for the report APIs.
 *
 * <AUTHOR> Vuong
 */
public class ReportController extends Controller {

    @Inject
    private DateBasedReportService dateBasedReportService;

    /**
     * Sends the monthly report for the given criteria to the client side.
     */
    public void findMonthlyReportForMerchant() {
        UserId merchantId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        MonthlyReportRequest request = getRouteContext()
                .createEntityFromParameters(MonthlyReportRequest.class);
        getRouteContext().json().send(dateBasedReportService
                .findMonthlyReportFor(merchantId.getAccountId(), request));
    }

    /**
     * Sends the performance report for the given criteria to the client side.
     */
    public void findPerformanceReport() {
        PerformanceReportRequest request = getRouteContext()
                .createEntityFromParameters(PerformanceReportRequest.class);
        getRouteContext().json()
                .send(dateBasedReportService.findPerformanceReportBy(request));
    }

    /**
     * Sends the daily report for the given criteria to the client side.
     */
    public void findDailyReport() {
        DailyReportRequest request = getRouteContext()
                .createEntityFromParameters(DailyReportRequest.class);
        getRouteContext().json()
                .send(dateBasedReportService.findDailyReportFor(request));
    }

}
