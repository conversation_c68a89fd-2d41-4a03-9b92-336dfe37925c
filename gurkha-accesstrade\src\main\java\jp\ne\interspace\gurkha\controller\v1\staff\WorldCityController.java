/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.List;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.persist.hussar.model.staff.WorldCityAndCountry;
import jp.ne.interspace.gurkha.service.staff.WorldCityService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;

/**
 * Controller for handling world city detail.
 *
 * <AUTHOR>
 */
public class WorldCityController extends Controller {

    private static final String SEARCH_CITY_PARAMETER = "searchCity";

    @Inject
    private WorldCityService worldCityService;

    /**
     * Sends the world city details to the client side.
     */
    public void findWorldCityDetails() {
        String searchCity = getRouteContext().getParameter(SEARCH_CITY_PARAMETER)
                .toString();
        List<WorldCityAndCountry> response = worldCityService.findWorldCitiesAndCountries(
                searchCity);
        getResponse().json().send(response);
    }

    /**
     * Find cities by given Country code.
     */
    public void findCities() {
        String countryCode = getRouteContext()
                .getParameter(COUNTRY_CODE_LOCAL_PARAMETER).toString();
        getRouteContext().json().send(worldCityService.findCitiesBy(countryCode));
    }
}
