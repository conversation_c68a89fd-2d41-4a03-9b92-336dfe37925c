/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.publisher.EmailDuplicationCheckRequest;
import jp.ne.interspace.gurkha.model.publisher.SiteUrlDuplicationCheckRequest;
import jp.ne.interspace.gurkha.model.publisher.UsernameDuplicationCheckRequest;
import jp.ne.interspace.gurkha.service.publisher.DuplicationCheckService;

/**
 * Controller for checking duplicate data.
 *
 * <AUTHOR> href="mailto:<EMAIL>">Ji<PERSON><PERSON></a>
 */
public class DuplicationCheckController extends Controller {

    @Inject
    private DuplicationCheckService duplicationCheckService;

    /**
     * Duplication check for the email address sent in the request body.
     */
    public void checkDuplicatedEmail() {
        EmailDuplicationCheckRequest request = getRouteContext().createEntityFromBody(
                        EmailDuplicationCheckRequest.class);

        getRouteContext().text().send(
                duplicationCheckService.isDuplicate(request));
    }

    /**
     * Duplication check for the username sent in the request body.
     */
    public void checkDuplicatedUsername() {
        UsernameDuplicationCheckRequest request = getRouteContext()
                .createEntityFromBody(UsernameDuplicationCheckRequest.class);

        getRouteContext().text().send(duplicationCheckService.isDuplicate(request));
    }

    /**
     * Duplication check for the website URL sent in the request body.
     */
    public void checkDuplicatedWebSite() {
        SiteUrlDuplicationCheckRequest request =
                getRouteContext().createEntityFromBody(
                        SiteUrlDuplicationCheckRequest.class);

        getRouteContext().text().send(
                duplicationCheckService.isDuplicate(request));
    }
}
