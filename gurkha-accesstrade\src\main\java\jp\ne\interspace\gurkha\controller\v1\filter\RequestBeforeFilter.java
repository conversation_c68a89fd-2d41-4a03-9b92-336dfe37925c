/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.filter;

import java.util.Locale;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import ro.pippo.controller.Controller;
import ro.pippo.core.Request;

import jp.ne.interspace.gurkha.auth.v1.ApiPermissionChecker;
import jp.ne.interspace.gurkha.auth.v1.ApiUserTypeChecker;
import jp.ne.interspace.gurkha.auth.v1.RequestAuthenticator;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.ResponseStatus;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.LANG;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.MERCHANT;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.PUBLISHER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.STAFF;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_TYPE;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.BIND_KEY_DEFAULT_LANGUAGE;

/**
 * Before filters for API requests.
 *
 * <AUTHOR> OBS DEV Team
 */
public class RequestBeforeFilter extends Controller {

    private static final String USER_TYPE_LOCAL_PARAMETER = "userType";

    @Inject @Named(BIND_KEY_DEFAULT_LANGUAGE)
    private String defaultLanguage;

    @Inject
    private RequestAuthenticator authenticator;

    @Inject
    private ApiPermissionChecker permissionChecker;

    @Inject
    private ApiUserTypeChecker apiUserTypeChecker;

    /**
     * Validates {@code X-ACCESSTRADE-USER-TYPE} header.
     * <p/>
     * `X-ACCESSTRADE-USER-TYPE` HTTP header is expected in each API request:
     * <ul>
     * <li>if the value is {@code merchant}, the JWT token is authenticated as the token
     * of a merchant</li>
     * <li>if the value is {@code publisher}, the JWT token is authenticated as the token
     * of a publisher</li>
     * <li>if the value is {@code staff}, the JWT token is authenticated as the token
     * of a staff</li>
     * <li>If the header contains any valid user type then that user type string will
     * be saved to local storage.</li>
     * <li>if the header is not set or set with a invalid string other than
     * {@code merchant} or {@code publisher} or {@code staff}, the JWT token is
     * invalidated and responded with {@code 400 Bad Request}</li>
     * </ul>
     */
    public void checkUserTypeHeader() {
        String userTypeHeader = getRouteContext().getRequest().getHeader(USER_TYPE);
        String type = Strings.nullToEmpty(userTypeHeader).trim();
        if (!type.equalsIgnoreCase(PUBLISHER) && !type.equalsIgnoreCase(MERCHANT)
                && !type.equalsIgnoreCase(STAFF)) {
            throw new GurkhaApplicationException("invalid user type: " + type,
                    ResponseStatus.BAD_REQUEST);
        }
        getRouteContext().setLocal(USER_TYPE_LOCAL_PARAMETER, type);
    }

    /**
     * Validates request JWT(JSON Web Token) and sends a 401 response if failed.
     * <p>
     * The authorization must be specified with a bearer JWT which consists of 1
     * JWT header property - {@code alg}, and 2 JWT claims - {@code sub} and
     * {@code iat}.
     * </p>
     * {@code sub} should be specified with a publisher user's uid, and
     * {@code iat} must be an epoch time in seconds for signature validation.
     *
     * <pre>
     * e.g. {"alg":"HS256"}{"sub":"v5542527tv3vw4ts6suvswssss2137xx","iat":1477204353}
     * </pre>
     */
    public void validateRequestJwt() {
        if (authenticator.isValidJwtOn(getRouteContext())) {
            proceedToTheNextHandler();
        } else {
            throw new GurkhaApplicationException("JWT auth failed!",
                    ResponseStatus.UNAUTHORIZED);
        }
    }

    /**
     * Sets the {@link Locale} to be used when handling the current request.
     */
    public void setLocaleFromRequest() {
        getRouteContext().setLocal(LOCALE_LOCAL_PARAMETER,
                Locale.forLanguageTag(extractLanguageFrom(getRequest())));
        proceedToTheNextHandler();
    }

    /**
     * Validates permission by API and sends a 403 response if failed.
     */
    public void validateApiPermission() {
        if (permissionChecker.isValidApiPermission(getRouteContext())) {
            proceedToTheNextHandler();
        } else {
            throw new GurkhaApplicationException("You don't have permission",
                    ResponseStatus.FORBIDDEN);
        }
    }

    /**
     * Validates user type by API and sends a 403 response if failed.
     */
    public void validateApiUserType() {
        if (apiUserTypeChecker.isValidApiUserType(getRouteContext())) {
            proceedToTheNextHandler();
        } else {
            throw new GurkhaApplicationException("You are not allowed to access this API",
                    ResponseStatus.FORBIDDEN);
        }
    }

    private void proceedToTheNextHandler() {
        getRouteContext().next();
    }

    private String extractLanguageFrom(Request request) {
        String languageFromRequest = request.getParameter(LANG).toString();
        String language = !Strings.isNullOrEmpty(languageFromRequest)
                && !Strings.isNullOrEmpty(
                        Locale.forLanguageTag(languageFromRequest).getLanguage())
                ? languageFromRequest : getDefaultLanguage();
        return language;
    }

    @VisibleForTesting
    public String getDefaultLanguage() {
        return defaultLanguage;
    }

}
