/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.filter;

import java.util.List;
import java.util.regex.Pattern;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.inject.Inject;

import lombok.Getter;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.auth.v2.RequestAuthenticator;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.RateLimitingApiGroup;
import jp.ne.interspace.gurkha.model.ResponseStatus;
import jp.ne.interspace.gurkha.module.CustomRateLimitingApiGroups;
import jp.ne.interspace.gurkha.module.DefaultRateLimitingApiGroup;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.HYPHEN;
import static lombok.AccessLevel.PACKAGE;

/**
 * Filter for performing rate-limitng for all gurkha APIs.
 *
 * <AUTHOR> Varga
 */
public class RateLimitingFilter extends Controller {

    private static final String REQUEST_LIMIT_EXCEEDED_ERROR_MESSAGE = "You have "
            + "exceeded the maximum number of requests allowed in a given timeframe. "
            + "Please try to send the request again later.";

    @Inject @CustomRateLimitingApiGroups @Getter(PACKAGE) @VisibleForTesting
    private List<RateLimitingApiGroup> customApiGroups;

    @Inject @DefaultRateLimitingApiGroup @Getter(PACKAGE) @VisibleForTesting
    private RateLimitingApiGroup defaultApiGroup;

    @Inject
    private JedisPool jedisPool;

    @Inject
    private RequestAuthenticator authenticator;

    /**
     * Performs rate limiting for the current API call. Throws a
     * {@code 429 - Too Many Requests} error, if this call exceeds the rate limit for the
     * current IP address and rate limiting settings.
     */
    public void performRateLimitingForCurrentRequest() {
        RateLimitingApiGroup apiGroup = getApiGroupForCurrentRequest();
        String redisKey = Joiner.on(HYPHEN).join(apiGroup.getName(),
                authenticator.getClientIpFrom(getRequest()));
        int totalTimeBucketCount = apiGroup.getTimeframeInMinutes() * 2;
        long currentBucket = getCurrentTimeInMinutes() % totalTimeBucketCount;

        try (Jedis jedis = jedisPool.getResource()) {
            addCurrentRequestToRequestCounts(jedis, apiGroup, redisKey,
                    totalTimeBucketCount, currentBucket);
            long requestCount = calculateRequestCount(jedis, apiGroup, redisKey,
                    totalTimeBucketCount, currentBucket);
            validateRequestCount(requestCount, apiGroup);
        }

        proceedToTheNextHandler();
    }

    @VisibleForTesting
    RateLimitingApiGroup getApiGroupForCurrentRequest() {
        String requestMethod = getRequest().getMethod();
        String requestPath = getRequest().getPath();
        for (RateLimitingApiGroup apiGroup : getCustomApiGroups()) {
            if (apiGroup.getApiMethodPathMapping().containsKey(requestMethod)) {
                for (Pattern requestPathPattern : apiGroup.getApiMethodPathMapping()
                        .get(requestMethod)) {
                    if (requestPathPattern.matcher(requestPath).matches()) {
                        return apiGroup;
                    }
                }
            }
        }
        return getDefaultApiGroup();
    }

    @VisibleForTesting
    long getCurrentTimeInMinutes() {
        return System.currentTimeMillis() / 60000;
    }

    @VisibleForTesting
    void addCurrentRequestToRequestCounts(Jedis jedis, RateLimitingApiGroup apiGroup,
            String redisKey, int timeBucketCount, long currentBucket) {
        String[] bucketsToClear = new String[apiGroup.getTimeframeInMinutes()];
        for (int i = 0; i < bucketsToClear.length; i++) {
            bucketsToClear[i] = String
                    .valueOf((currentBucket + i + 1) % timeBucketCount);
        }

        Pipeline pipeline = jedis.pipelined();
        pipeline.multi();
        pipeline.hincrBy(redisKey, String.valueOf(currentBucket), 1);
        pipeline.hdel(redisKey, bucketsToClear);
        pipeline.expire(redisKey, apiGroup.getTimeframeInMinutes() * 60);
        pipeline.exec();
        pipeline.sync();
    }

    @VisibleForTesting
    long calculateRequestCount(Jedis jedis, RateLimitingApiGroup apiGroup,
            String redisKey, int timeBucketCount, long currentBucket) {
        String[] bucketsToCheck = new String[apiGroup.getTimeframeInMinutes()];
        for (int i = 0; i < bucketsToCheck.length; i++) {
            bucketsToCheck[i] = String
                    .valueOf(Math.floorMod(currentBucket - i, timeBucketCount));
        }

        List<String> rawRequestCounts = jedis.hmget(redisKey, bucketsToCheck);

        long requestCount = 0;
        for (String rawRequestCount : rawRequestCounts) {
            try {
                requestCount += Long.parseLong(rawRequestCount);
            } catch (NumberFormatException ex) {
                // the specific bucket did not exist, no need to do anything
            }
        }
        return requestCount;
    }

    @VisibleForTesting
    void validateRequestCount(long requestCount, RateLimitingApiGroup apiGroup) {
        if (requestCount > apiGroup.getMaxAllowedRequests()) {
            throw new GurkhaApplicationException(REQUEST_LIMIT_EXCEEDED_ERROR_MESSAGE,
                    ResponseStatus.TOO_MANY_REQUESTS);
        }
    }

    private void proceedToTheNextHandler() {
        getRouteContext().next();
    }
}
