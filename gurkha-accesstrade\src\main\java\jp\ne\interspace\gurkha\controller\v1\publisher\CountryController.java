/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.publisher.CountryService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for country-related APIs for logged-in publishers.
 *
 * <AUTHOR>
 */
public class CountryController extends Controller {

    @Inject
    private CountryService countryService;

    @Inject
    private jp.ne.interspace.gurkha.service.CountryService commonCountryService;

    /**
     * Sends the details of company of the current country.
     */
    public void findCompanyDetails() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().json().send(countryService.findCompanyDetailsBy(countryCode));
    }

    /**
     * Sends the FAQ details of the current country.
     */
    public void findFaqDetails() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().json().send(countryService.findFaqDetailsBy(countryCode));
    }

    /**
     * Sends the minimum payment details of the current country.
     */
    public void findMinimumPaymentDetails() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().json().send(countryService.findMinimumPaymentDetailsBy(
                countryCode, publisherId.getAccountId()));
    }

    /**
     * Sends the flag of legacy publisher identification data enabled flag of
     * the current country.
     */
    public void isLegacyPublisherIdentificationDataEnabled() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().text().send(countryService
                .isLegacyPublisherIdentificationDataEnabled(countryCode));
    }

    /**
     * Sends the zoneId of the current country.
     */
    public void findZoneId() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().text().send(commonCountryService.findZoneIdBy(countryCode));
    }

    /**
     * Sends the flag of super point enabled flag of the current country.
     */
    public void isSuperPointEnabled() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().text().send(countryService.isSuperPointEnabled(countryCode));
    }

    /**
     * Sends the url accessible validation enable flag of the current country.
     */
    public void isUrlAccessibleValidationEnabled() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getRouteContext().text()
                .send(countryService.isUrlAccessibleValidationEnabled(countryCode));
    }
}
