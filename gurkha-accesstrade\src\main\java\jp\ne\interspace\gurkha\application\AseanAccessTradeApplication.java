/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.application;

import java.time.Instant;

import com.google.common.base.Joiner;
import com.google.inject.Inject;
import com.google.inject.Module;
import com.google.inject.name.Named;

import jp.ne.interspace.gurkha.controller.CountryController;
import jp.ne.interspace.gurkha.controller.CurrencyController;
import jp.ne.interspace.gurkha.controller.PublisherController;
import jp.ne.interspace.gurkha.controller.filter.RateLimitingFilter;
import jp.ne.interspace.gurkha.controller.publisher.DuplicationCheckController;
import jp.ne.interspace.gurkha.controller.publisher.PublisherAccountActivationController;
import jp.ne.interspace.gurkha.controller.publisher.PublisherPasswordRecoveryController;
import jp.ne.interspace.gurkha.controller.publisher.PublisherSignUpController;
import jp.ne.interspace.gurkha.controller.publisher.TrackingController;
import jp.ne.interspace.gurkha.controller.v1.ShorterUrlController;
import jp.ne.interspace.gurkha.controller.v1.filter.RequestBankAccountFilter;
import jp.ne.interspace.gurkha.controller.v1.filter.RequestBeforeFilter;
import jp.ne.interspace.gurkha.controller.v1.merchant.ConversionController;
import jp.ne.interspace.gurkha.controller.v1.merchant.MerchantAuthenticationController;
import jp.ne.interspace.gurkha.controller.v1.publisher.AffiliationController;
import jp.ne.interspace.gurkha.controller.v1.publisher.BankAccountController;
import jp.ne.interspace.gurkha.controller.v1.publisher.CampaignController;
import jp.ne.interspace.gurkha.controller.v1.publisher.CategoryController;
import jp.ne.interspace.gurkha.controller.v1.publisher.CreativeController;
import jp.ne.interspace.gurkha.controller.v1.publisher.DecisionTreeController;
import jp.ne.interspace.gurkha.controller.v1.publisher.LogController;
import jp.ne.interspace.gurkha.controller.v1.publisher.NotificationController;
import jp.ne.interspace.gurkha.controller.v1.publisher.OtpController;
import jp.ne.interspace.gurkha.controller.v1.publisher.PaymentController;
import jp.ne.interspace.gurkha.controller.v1.publisher.ProductController;
import jp.ne.interspace.gurkha.controller.v1.publisher.PromoController;
import jp.ne.interspace.gurkha.controller.v1.publisher.PublisherAccountController;
import jp.ne.interspace.gurkha.controller.v1.publisher.PublisherAuthenticationController;
import jp.ne.interspace.gurkha.controller.v1.publisher.ReferralController;
import jp.ne.interspace.gurkha.controller.v1.publisher.ReportController;
import jp.ne.interspace.gurkha.controller.v1.publisher.SiteController;
import jp.ne.interspace.gurkha.controller.v1.publisher.SupportController;
import jp.ne.interspace.gurkha.controller.v1.staff.AdPlatformController;
import jp.ne.interspace.gurkha.controller.v1.staff.AutomaticUpdateConversionRankHistoriesController;
import jp.ne.interspace.gurkha.controller.v1.staff.AutomaticUpdateConversionRankSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.BonusController;
import jp.ne.interspace.gurkha.controller.v1.staff.CampaignBudgetController;
import jp.ne.interspace.gurkha.controller.v1.staff.CampaignClosureController;
import jp.ne.interspace.gurkha.controller.v1.staff.CampaignConversionFieldMappingController;
import jp.ne.interspace.gurkha.controller.v1.staff.CampaignReferralSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.CampaignRelationshipController;
import jp.ne.interspace.gurkha.controller.v1.staff.CampaignScheduleController;
import jp.ne.interspace.gurkha.controller.v1.staff.CategoryRewardSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.CreativeGroupController;
import jp.ne.interspace.gurkha.controller.v1.staff.CurrencyExchangeRateController;
import jp.ne.interspace.gurkha.controller.v1.staff.EmailController;
import jp.ne.interspace.gurkha.controller.v1.staff.EmailTemplateController;
import jp.ne.interspace.gurkha.controller.v1.staff.FixedFeeMasterController;
import jp.ne.interspace.gurkha.controller.v1.staff.GlobalRewardSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.GoogleKeywordAnalyticController;
import jp.ne.interspace.gurkha.controller.v1.staff.GoogleKeywordAnalyticSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.HourlyRewardPriceSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.MerchantAccountController;
import jp.ne.interspace.gurkha.controller.v1.staff.MerchantCustomerCountryController;
import jp.ne.interspace.gurkha.controller.v1.staff.MonthlyClosureController;
import jp.ne.interspace.gurkha.controller.v1.staff.PermissionController;
import jp.ne.interspace.gurkha.controller.v1.staff.ProductFeedSynchronizationController;
import jp.ne.interspace.gurkha.controller.v1.staff.ProductRewardSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.ResultTargetMasterController;
import jp.ne.interspace.gurkha.controller.v1.staff.ResultTargetSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.RewardSettingController;
import jp.ne.interspace.gurkha.controller.v1.staff.StaffAccountController;
import jp.ne.interspace.gurkha.controller.v1.staff.StaffAuthenticationController;
import jp.ne.interspace.gurkha.controller.v1.staff.WorldCityController;
import jp.ne.interspace.gurkha.controller.v2.internalteam.CampaignsStatisticsDataController;
import jp.ne.interspace.gurkha.controller.v2.internalteam.PublisherRankController;
import jp.ne.interspace.gurkha.module.AseanAccessTradeMainModule;

import static jp.ne.interspace.gurkha.config.GurkhaConfig.getCountry;
import static jp.ne.interspace.gurkha.config.GurkhaConfig.getEnvironment;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.SPACE;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.BIND_KEY_RATE_LIMITING_ENABLED;

/**
 * Gurkha application class for the backend service of Asean AccessTrade.
 *
 * <AUTHOR> OBS DEV Team
 */
public class AseanAccessTradeApplication extends AbstractGurkhaApplication {

    private static String APPLICATION_INDENTIFIER = Joiner.on(SPACE)
            .join(getCountry().name(), getEnvironment().name(), SPACE);

    @Inject @Named(BIND_KEY_RATE_LIMITING_ENABLED)
    private boolean rateLimitingIsEnabled;

    @Override
    protected Module[] installModules() {
        return new Module[] {
                // add only independent modules on an as-needed basis
                new AseanAccessTradeMainModule() };
    }

    @Override
    protected void preInit() {

        if (rateLimitingIsEnabled) {
            ALL("/.+", RateLimitingFilter.class, "performRateLimitingForCurrentRequest");
        }

        ALL("/.+", RequestBeforeFilter.class, "setLocaleFromRequest");

        GET("/robots.txt", (routeContext) -> routeContext.getResponse()
                .header("Content-Type", "text/plain").send("User-agent: *\nDisallow: /"));

        GET("/ping", (routeContext) -> routeContext.getResponse().ok()
                .send(APPLICATION_INDENTIFIER + getApplicationVersion()));

        GET("/timestamp", (routeContext) -> routeContext.getResponse().ok()
                .send(String.valueOf(Instant.now().getEpochSecond())));

        GET("/auth/{username: [a-zA-Z0-9\\._-]+@?[a-zA-Z0-9\\._-]+}",
                PublisherAuthenticationController.class, "authenticateCredentials");

        GET("/influencer/auth/{username: [a-zA-Z0-9\\._-]+@?[a-zA-Z0-9\\._-]+}",
                PublisherAuthenticationController.class,
                "influencerAuthenticateCredentials");

        GET("/publishers/auth/{username: [a-zA-Z0-9\\._-]+@?[a-zA-Z0-9\\._-]+}",
                PublisherAuthenticationController.class, "authenticateCredentials");

        POST("/publishers/auth/oauth", PublisherAuthenticationController.class,
                "authenticateOauthCredentials");

        GET("/merchants/auth/{username: [a-zA-Z0-9\\._-]+@?[a-zA-Z0-9\\._-]+}",
                MerchantAuthenticationController.class, "authenticateCredentials");

        GET("/staff/auth/{username: [a-zA-Z0-9\\._-]+@?[a-zA-Z0-9\\._-]+}",
                StaffAuthenticationController.class, "authenticateCredentials");

        ALL("/v1/.*", RequestBeforeFilter.class, "checkUserTypeHeader");

        ALL("/v1/.*", RequestBeforeFilter.class, "validateApiUserType");

        ALL("/v1/.*", RequestBeforeFilter.class, "validateRequestJwt");

        ALL("/v1/.*", RequestBeforeFilter.class, "validateApiPermission");

        ALL("/v1/publishers/me/bankaccount.*", RequestBankAccountFilter.class,
                "validateOtpTokenRequest");

        ALL("/v2/.*",
                jp.ne.interspace.gurkha.controller.v2.filter.RequestBeforeFilter.class,
                "validateRequest");
    }

    @Override
    protected void onInit() {
        GET("/accessible/site/url", SiteController.class, "isAccessibleUrl");

        POST("/activateaccount", PublisherAccountActivationController.class,
                "activatePublisherAccount");

        POST("/passwordrecovery", PublisherPasswordRecoveryController.class,
                "initiatePasswordRecovery");

        POST("/resendActivationLink", PublisherAccountActivationController.class,
                "resendActivationLink");

        POST("/resetpassword", PublisherPasswordRecoveryController.class,
                "resetPassword");

        POST("/signup/individual", PublisherSignUpController.class,
                "createIndividualAccountWithSite");

        POST("/signup/corporation", PublisherSignUpController.class,
                "createCorporateAccountWithSite");

        POST("/dupecheck/email", DuplicationCheckController.class,
                "checkDuplicatedEmail");

        POST("/dupecheck/username", DuplicationCheckController.class,
                "checkDuplicatedUsername");

        POST("/dupecheck/website", DuplicationCheckController.class,
                "checkDuplicatedWebSite");

        GET("/campaigns/conversion-tracking-details/enabled", TrackingController.class,
                "findEnabledConversionTrackingDetails");

        GET("/campaigns/landing-page-tracking-details/enabled", TrackingController.class,
                "findEnabledLandingPageTrackingDetails");

        GET("/categories/master",
                jp.ne.interspace.gurkha.controller.CategoryMasterController.class,
                "findAllCategoryMasters");

        GET("/countries", CountryController.class, "findAll");

        GET("/countries/active", CountryController.class, "findActiveCountries");

        GET("/countries/influencer/active", CountryController.class,
                "findActiveCountriesForInfluencer");

        GET("/countries/active/codes-names", CountryController.class,
                "findActiveCountryCodesAndNames");

        POST("/logs/frontend/error", LogController.class, "logFrontendError");

        GET("/products/{productId}",
                jp.ne.interspace.gurkha.controller.publisher.ProductController.class,
                "findProduct");
        GET("/publishers/site/{siteId: \\d+}/campaign/{campaignId: \\d+}/productfeed/{creativeId: \\d+}/csv/{apiKey}",
                PublisherController.class, "downloadCsvProductFeed");

        GET("/publishers/sso-auth", PublisherController.class,
                "findDetailsFromSingleSingOnToken");

        GET("/v1/banks", BankAccountController.class, "findAllBanks");

        GET("/v1/bankaccounts/types", BankAccountController.class,
                "findAllBankAccountTypes");

        GET("/v1/campaigns", CampaignController.class, "findCampaigns");

        POST("/v1/campaigns/affiliate", AffiliationController.class,
                "applyForAffiliation");

        GET("/v1/campaigns/customer-countries", CampaignController.class,
                "findCustomerCountries");

        GET("/v1/campaigns/latest", CampaignController.class, "findLatestCampaigns");

        GET("/v1/campaigns/name/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "findCampaignName");

        GET("/v1/campaigns/result/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "findCampaignResultIds");

        GET("/v1/campaigns/promoted", CampaignController.class, "findPromotedCampaigns");

        GET("/v1/campaigns/running/names-ids", CampaignController.class,
                "findRunningCampaignNamesAndIds");

        GET("/v1/campaigns/statuses",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "findAllCampaignStatuses");

        GET("/v1/campaigns/types",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "findAllCampaignTypes");

        GET("/v1/campaigns/{campaignId: \\d+}", CampaignController.class,
                "findCampaignDetails");

        GET("/v1/campaigns/influencer-description/{campaignId: \\d+}", CampaignController.class,
                "findInfluencerCampaignDescription");

        GET("/v1/campaigns/{campaignId: \\d+}/creatives/special/availability",
                CreativeController.class, "checkSpecialCreativeAvailabilityForCampaign");

        GET("/v1/campaigns/productCategories",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "findProductCategories");

        GET("/v1/campaigns/{campaignId: \\d+}/hourly-reward",
                CampaignController.class, "findHourlyRewards");

        GET("/v1/categories", CategoryController.class, "findAll");

        GET("/v1/countries/company",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "findCompanyDetails");
        GET("/v1/countries/currency",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findCurrency");
        GET("/v1/countries/currency/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findCurrencyByCountryCode");
        GET("/v1/countries/faq-details",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "findFaqDetails");
        GET("/v1/countries/is-legacy-publisher-identification-data-enabled",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "isLegacyPublisherIdentificationDataEnabled");
        GET("/v1/countries/is-super-point-enabled",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "isSuperPointEnabled");
        GET("/v1/countries/is-url-accessible-validation-enabled",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "isUrlAccessibleValidationEnabled");
        GET("/v1/countries/is-pending-for-merchant-payment",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "isPendingForMerchantPayment");
        GET("/v1/countries/is-merchant-payment-approved-enabled",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "isMerchantPaymentApprovedEnabled");
        GET("/v1/countries/minimum-payment-details",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "findMinimumPaymentDetails");
        GET("/v1/countries/permitted",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findPermittedCountries");
        GET("/v1/countries/zone-id",
                jp.ne.interspace.gurkha.controller.v1.publisher.CountryController.class,
                "findZoneId");

        GET("/v1/currencies", CurrencyController.class, "findCurrencies");
        GET("/v1/currencies/available", CurrencyController.class,
                "findAvailableCurrencies");

        GET("/v1/merchants/me/conversions", ConversionController.class,
                "findConversions");
        GET("/v1/merchants/me/conversions/count-conversion-result",
                ConversionController.class, "countConversionResult");
        GET("/v1/merchants/me/conversions/dashboard-result", ConversionController.class,
                "findConversionResults");

        GET("/v1/merchants/global/campaigns/synchronizable/{campaignId: \\d+}/country/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "isSynchronizable");
        POST("/v1/merchants/global/creatives",
                jp.ne.interspace.gurkha.controller.v1.CreativeController.class,
                "createClonedCreatives");
        PUT("/v1/merchants/global/creatives/status",
                jp.ne.interspace.gurkha.controller.v1.CreativeController.class,
                "updateClonedCreativeStatus");

        GET("/v1/merchants/reports/daily",
                jp.ne.interspace.gurkha.controller.v1.merchant.ReportController.class,
                "findDailyReport");
        GET("/v1/merchants/reports/monthly",
                jp.ne.interspace.gurkha.controller.v1.merchant.ReportController.class,
                "findMonthlyReportForMerchant");
        GET("/v1/merchants/reports/performance",
                jp.ne.interspace.gurkha.controller.v1.merchant.ReportController.class,
                "findPerformanceReport");

        GET("/v1/postback/conversiondata/types", SiteController.class,
                "findAllPostbackParameterConversionDataTypes");
        GET("/v1/postback/sub-ids", SiteController.class, "findAllPostbackSubIds");

        GET("/v1/publishers/me/promo-categories", PromoController.class,
                "findPromoCategories");
        GET("/v1/publishers/me/promos", PromoController.class, "findPromos");

        GET("/v1/publishers/me/reports/conversion", ReportController.class,
                "findConversionReportForPublisher");
        GET("/v1/publishers/me/reports/conversion/download-report",
                ReportController.class, "downloadConversionReportForPublisherCsv");
        GET("/v1/publishers/me/reports/conversion-summary", ReportController.class,
                "findConversionReportSummary");
        GET("/v1/publishers/me/reports/conversion/campaign-summary",
                ReportController.class, "findConversionCampaignReportForPublisher");
        GET("/v1/publishers/me/reports/conversion/detail", ReportController.class,
                "findConversionReportDetails");
        GET("/v1/publishers/me/reports/conversion/grouped-by-transaction",
                ReportController.class, "findConversionGroupedByTransactionReport");
        GET("/v1/publishers/me/reports/conversion/product-detail", ReportController.class,
                "findProducts");
        GET("/v1/publishers/me/reports/custom", ReportController.class,
                "findCustomReportForPublisher");
        GET("/v1/publishers/me/reports/daily/device", ReportController.class,
                "findDailyDeviceReportForPublisher");
        GET("/v1/publishers/me/reports/monthly/device", ReportController.class,
                "findMonthlyDeviceReportForPublisher");
        GET("/v1/publishers/me/reports/monthly", ReportController.class,
                "findMonthlyReportForPublisher");
        GET("/v1/publishers/me/reports/reward/detail", ReportController.class,
                "findRewardReport");
        GET("/v1/publishers/me/reports/summary", ReportController.class,
                "findSummaryReportForPublisher");
        GET("/v1/publishers/me/reports/summary/engagement", ReportController.class,
                "findEngagementSummaryReportForPublisher");
        GET("/v1/publishers/me/reports/summary/reward", ReportController.class,
                "findRewardSummaryReportForPublisher");
        GET("/v1/publishers/me/reports/campaign", ReportController.class,
                "findCampaignReportForPublisher");
        GET("/v1/publishers/me/reports/creative", ReportController.class,
                "findCreativeReportForPublisher");
        GET("/v1/publishers/me/reports/referee", ReportController.class,
                "findRefereeReport");
        GET("/v1/publishers/me/reports/referee-campaign", ReportController.class,
                "findRefereeCampaignReport");
        GET("/v1/publishers/me/reports/sub-id", ReportController.class,
                "findSubIdReportForPublisher");

        GET("/v1/publishers/me/account", PublisherAccountController.class,
                "findPublisherAccountDetails");
        GET("/v1/publisher/me/account-setting-checking", PublisherAccountController.class,
                "checkAccountSettings");
        GET("/v1/publisher/me/account-influencer-setting-checking",
                PublisherAccountController.class, "checkAccountSettingsForInfluencer");

        PUT("/v1/publishers/me/account/social-network", PublisherAccountController.class,
                "updateSocialNetwork");
        GET("/v1/publishers/me/account/sso-key", PublisherAccountController.class,
                "generateSingleSignOnKey");
        GET("/v1/publishers/me/account/sso-token", PublisherAccountController.class,
                "generateSingleSignOnToken");

        GET("/v1/publishers/me/account/email", PublisherAccountController.class,
                "findEmail");

        GET("/v1/publishers/me/account/is-activated", PublisherAccountController.class,
                "isAccountActivated");

        GET("/v1/publishers/me/account/is-global-publisher",
                PublisherAccountController.class, "isGlobalPublisher");

        GET("/v1/publishers/me/account/onboarding-question-progress",
                PublisherAccountController.class, "findOnboardingQuestionProgress");

        PUT("/v1/publishers/me/account/onboarding-question-progress",
                PublisherAccountController.class, "updateOnboardingQuestionProgress");

        GET("/v1/publishers/me/account/onboarding-status",
                PublisherAccountController.class, "findOnboardingStatus");

        PUT("/v1/publishers/me/account/onboarding-status",
                PublisherAccountController.class, "updateOnboardingStatus");

        GET("/v1/publishers/me/account/onboarding-task-progress",
                PublisherAccountController.class, "findOnboardingTaskProgress");

        GET("/v1/publishers/me/account/summary", PublisherAccountController.class,
                "findPublisherAccountSummary");

        PUT("/v1/publishers/me/account/corporate", PublisherAccountController.class,
                "updateCorporateAccount");

        GET("/v1/publishers/me/account/hashed-publisher-id",
                PublisherAccountController.class, "findHashedPublisherId");

        PUT("/v1/publishers/me/account/individual", PublisherAccountController.class,
                "updateIndividualAccount");

        GET("/v1/publishers/me/bankaccount", BankAccountController.class,
                "findBankAccountForPublisher");

        PUT("/v1/publishers/me/bankaccount", BankAccountController.class,
                "updateBankAccountForPublisher");

        GET("/v1/publishers/me/campaigns/promos-available/names",
                CampaignController.class, "findAvailablePromosCampaignForPublisher");

        GET("/v1/publishers/me/campaigns/affiliated-rejected-prohibited/names",
                CampaignController.class,
                "findAffiliatedRejectedProhibitedCampaignNamesAndIdsForPublisher");

        GET("/v1/publishers/me/campaigns/affiliated/names", CampaignController.class,
                "findAffiliatedCampaignNamesAndIdsForPublisher");

        GET("/v1/publishers/me/campaigns/availableForInfluencers/{siteId: \\d+}",
                CampaignController.class, "findCategoryCampaignAvailableForInfluencers");

        GET("/v1/publishers/me/campaigns/{campaignId: \\d+}/brand-bidding/keywords",
                CampaignController.class, "findBrandBiddingKeywords");

        GET("/v1/publishers/me/campaigns/campaign-status", CampaignController.class,
                "findCampaignStatus");

        GET("/v1/publishers/me/campaigns/{campaignId: \\d+}/creatives/summaries",
                CreativeController.class, "findCreativeSummaries");

        GET("/v1/publishers/me/campaigns/{campaignId: \\d+}/sites/{siteId: \\d+}/is-affiliated",
                AffiliationController.class, "isAffiliated");

        GET("/v1/publishers/me/country/country-code",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findCountryCode");
        GET("/v1/publishers/me/currency", PublisherAccountController.class,
                "findCurrency");

        GET("/v1/publishers/me/influencer/{siteId: \\d+}/creative/{creativeId: \\d+}",
                CreativeController.class, "findCustomCreativeDetailsForInfluencer");

        PUT("/v1/publishers/me/influencer/{siteId: \\d+}/creatives/custom",
                CreativeController.class, "updateCustomCreativeForInfluencer");

        POST("/v1/publishers/me/influencer/{siteId: \\d+}/creatives/custom",
                CreativeController.class, "createCustomCreativeForInfluencer");

        GET("/v1/publishers/me/influencer/{siteId: \\d+}/creatives/custom-links",
                CreativeController.class, "findCustomCreativesForInfluencer");

        GET("/v1/publishers/me/is-smart-link-tag-enabled", PublisherAccountController.class,
                "isSmartLinkTagEnabled");

        GET("/v1/publishers/me/influencer/notifications", NotificationController.class,
                "findNotificationsForInfluencers");

        GET("/v1/publishers/me/notifications", NotificationController.class,
                "findNotificationsForPublishers");

        POST("/v1/publishers/me/viewed/notifications", NotificationController.class,
                "insertViewedNotifications");

        PUT("/v1/publishers/me/password", PublisherAccountController.class,
                "changePublisherPassword");

        GET("/v1/publishers/me/payment", PaymentController.class, "findMonthlyPayments");

        GET("/v1/publishers/me/payment/process-stage-details", PaymentController.class,
                "findPaymentProcessStageDetails");

        GET("/v1/publishers/me/payment/invoice", PaymentController.class,
                "downloadInvoice");

        GET("/v1/publishers/me/payment/invoice-detail/{invoiceId}", PaymentController.class, "findInvoiceDetails");

        GET("/v1/publishers/me/payment/paid", PaymentController.class, "findPaidPayments");

        GET("/v1/publishers/me/payment-summary", PaymentController.class,
                "findPaymentSummary");

        POST("/v1/publishers/me/referral/referral-setting", ReferralController.class,
                "insertReferral");

        GET("/v1/publishers/me/referral/referral-setting/campaign-id",
                ReferralController.class, "findReferralCampaignId");

        GET("/v1/publishers/me/referral/referral-setting/is-enabled-influencer",
                ReferralController.class, "isEnabledInfluencer");

        GET("/v1/publishers/me/referral/referral-setting/is-referral-available",
                ReferralController.class, "isReferralAvailable");

        GET("/v1/publishers/me/reports/payment/find-pay-date", ReportController.class,
                "findMinMaxPaidDate");

        PUT("/v1/publishers/me/reports/conversion/async-export", ReportController.class,
                "requestConversionReportForAsyncExport");

        GET("/v1/publishers/me/sites", SiteController.class,
                "findNonDeletedSiteSummariesForPublisher");

        PUT("/v1/publishers/me/sites", SiteController.class, "upsertSiteForPublisher");

        GET("/v1/publishers/me/sites/{siteId: \\d+}", SiteController.class,
                "findNonDeletedSiteDetailsForPublisher");

        DELETE("/v1/publishers/me/sites/{siteId: \\d+}", SiteController.class,
                "deleteSiteForPublisher");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/affiliated/custom-creative-enabled-campaigns",
                CampaignController.class, "findCampaignsAndCustomRules");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/affiliated",
                CampaignController.class, "findAffiliatedCampaigns");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/applied",
                CampaignController.class, "findAppliedCampaigns");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/count-summary",
                CampaignController.class, "countCampaignSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/fastest-growing-summary",
                CampaignController.class, "findFastestGrowingCampaignSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/featured-summary",
                CampaignController.class, "findFeaturedCampaignSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/paused-summary",
                CampaignController.class, "findPausedCampaignSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/rejected",
                CampaignController.class, "findRejectedCampaigns");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/terminated-summary",
                CampaignController.class, "findTerminatedCampaignSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/unaffiliated",
                CampaignController.class, "findUnaffiliatedCampaigns");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/top-summary",
                CampaignController.class, "findTopCampaignSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/upsized-rewards",
                CampaignController.class, "findUpsizedCampaigns");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/custom",
                CreativeController.class, "findCustomCreatives");

        POST("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/custom",
                CreativeController.class, "createCustomCreativeForPublisher");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/custom/acceptedurls",
                CreativeController.class, "findCustomCreativeAcceptedUrlsForPublisher");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/image",
                CreativeController.class, "findImageCreatives");

        POST("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/multiple-custom",
                CreativeController.class, "createCustomCreatives");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/original",
                CreativeController.class, "findOriginalCreatives");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/quicklink",
                CreativeController.class, "findQuickLink");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/creatives/text",
                CreativeController.class, "findTextCreatives");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/productfeed/urls",
                CreativeController.class, "findLegacyProductFeeds");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/seocontents",
                CreativeController.class, "findSeoContentCreatives");

        POST("/v1/publishers/me/sites/{siteId: \\d+}/campaigns/{campaignId: \\d+}/seocontents",
                jp.ne.interspace.gurkha.controller.v1.publisher
                    .ConversionController.class, "requestSeoContentConfirmation");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/creatives/custom/summaries",
                CreativeController.class, "findCustomCreativeSummaries");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/postback", SiteController.class,
                "findPostbackDetailsForNonDeletedSite");

        PUT("/v1/publishers/me/sites/{siteId: \\d+}/postback", SiteController.class,
                "upsertPostbackDetailsForNonDeletedSite");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/subids", SiteController.class,
                "findSubIdsForNonDeletedSite");

        PUT("/v1/publishers/me/sites/{siteId: \\d+}/subids", SiteController.class,
                "upsertSubIdsForNonDeletedSite");

        POST("/v1/publishers/me/support/inquiry", SupportController.class,
                "sendSupportEmail");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/products/count",
                ProductController.class, "count");
        GET("/v1/publishers/me/sites/{siteId: \\d+}/products/detail/{productId}",
                ProductController.class, "findProductDetails");
        GET("/v1/publishers/me/sites/{siteId: \\d+}/products",
                ProductController.class, "findProductSummaries");
        GET("/v1/publishers/me/sites/decision-tree",
                DecisionTreeController.class, "findDecisionTreeData");
        GET("/v1/publishers/me/otp/{otp: \\d+}",
                OtpController.class, "generateOtpToken");

        GET("/v1/publishers/me/otp",
                OtpController.class, "requestOtp");

        GET("/v1/publisher/creatives/custom/find-first-id", CreativeController.class,
                "findFirstId");

        GET("/v1/publishers/creatives/availableForInfluencers", CreativeController.class,
                "findCreativesAvailableForInfluencers");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/generate-smart-link-tag-key",
                SiteController.class, "generateSmartLinkTagKey");

        GET("/v1/publishers/me/sites/{siteId: \\d+}/smart-link-tag-key",
                SiteController.class, "findSmartLinkTagKeyDetails");

        GET("/v1/publishers/me/sites/campaigns/creatives/image",
                CreativeController.class, "findBase64EncodedImage");

        GET("/v1/url/shorter", ShorterUrlController.class, "findShorterUrl");

        GET("/v1/sites/types",
                jp.ne.interspace.gurkha.controller.v1.SiteController.class,
                "findAllSiteTypes");

        GET("/v1/sites/types/non-empty",
                jp.ne.interspace.gurkha.controller.v1.SiteController.class,
                "findNonEmptySiteTypes");

        GET("/v1/staff/account/email", StaffAccountController.class, "findStaffEmail");
        GET("/v1/staff/ad-flatforms", AdPlatformController.class, "findAll");
        POST("/v1/staff/affiliations/mass-restriction",
                jp.ne.interspace.gurkha.controller.v1.staff.AffiliationController.class,
                "insertAffiliateMassRestriction");
        POST("/v1/staff/bonus/conversion", BonusController.class,
                "insertConversionBonus");
        POST("/v1/staff/bonus/fixed", BonusController.class, "insertFixedBonus");
        POST("/v1/staff/bonus/fixed-bonus/register-csv", BonusController.class,
                "insertFixedBonusCsv");
        PUT("/v1/staff/bonus/fixed-status", BonusController.class,
                "updateFixedBonusStatus");

        GET("/v1/staff/campaign-closure", CampaignClosureController.class,
                "findAllCampaignClosure");
        POST("/v1/staff/campaign-closure/async-create", CampaignClosureController.class,
                "upsertCampaignClosure");
        PUT("/v1/staff/campaign-closure/async-export",
                CampaignClosureController.class, "requestCampaignClosureForAsyncExport");
        PUT("/v1/staff/campaign-closure/upsert-validating",
                CampaignClosureController.class, "upsertValidating");
        GET("/v1/staff/campaign-closure/find-summary", CampaignClosureController.class,
                "findCampaignClosureSummary");
        GET("/v1/staff/campaign-closure/request-summary", CampaignClosureController.class,
                "requestCampaignClosureSummary");

        GET("/v1/staff/campaigns/auto-action-appr-durations",
                ResultTargetSettingController.class,
                "findCampaignAutoActionApprovedDurations");
        PUT("/v1/staff/campaigns/auto-action-appr-durations",
                ResultTargetSettingController.class,
                "updateCampaignAutoActionApprovedDurations");
        PUT("/v1/staff/campaigns/auto-affiliation-approval",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "updateCampaignAutoAffiliationApproval");

        GET("/v1/staff/campaigns/categories",
                jp.ne.interspace.gurkha.controller.v1.staff.CategorySiteController.class,
                "findCampaignCategories");
        GET("/v1/staff/campaigns/category-ids",
                jp.ne.interspace.gurkha.controller.v1.staff.CategorySiteController.class,
                "findCampaignCategoryIds");

        GET("/v1/staff/campaigns/featured-campaigns",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findFeaturedCampaigns");
        PUT("/v1/staff/campaigns/featured-campaigns/upsert",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "upsertFeaturedCampaigns");
        GET("/v1/staff/campaigns/{campaignId: \\d+}/field-mappings",
                CampaignConversionFieldMappingController.class, "findByCampaign");
        POST("/v1/staff/campaigns/field-mappings/upsert",
                CampaignConversionFieldMappingController.class, "upsertByCampaign");

        GET("/v1/staff/campaigns/fixed-fee-master", FixedFeeMasterController.class,
                "findAllFixedFeeMasters");

        GET("/v1/staff/campaigns/names-ids",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findCampaignNamesAndIds");

        GET("/v1/staff/countries/find-pending-for-merchant-payment-configurations",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findPendingForMerchantPaymentConfigurations");
        GET("/v1/staff/countries/find-merchant-payment-approved-enable-configurations",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findMerchantPaymentApprovedEnableConfigurations");
        GET("/v1/staff/countries/is-encoded-type-enabled/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "isEncodedTypeEnabled");

        GET("/v1/staff/campaigns/name-country-code/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findCampaignNameAndCountryCode");
        GET("/v1/staff/campaigns/brand-bidding-detection/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findBrandBiddingDetectionSetting");
        GET("/v1/staff/campaigns/brand-bidding-detection/details",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findBrandBiddingDetectionSettings");
        GET("/v1/staff/campaigns/original-country-code/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findOriginalCountryCode");

        GET("/v1/staff/campaigns/relationship", CampaignRelationshipController.class,
                "findCampaignRelationship");
        POST("/v1/staff/campaigns/relationship", CampaignRelationshipController.class,
                "upsertCampaignRelationship");
        GET("/v1/staff/campaigns/result-target-master",
                ResultTargetMasterController.class, "findAllResultTargetMasters");
        GET("/v1/staff/campaigns/result-target-setting",
                ResultTargetSettingController.class, "findCampaignResultTargetSettings");

        DELETE("/v1/staff/campaigns/schedule", CampaignScheduleController.class,
                "deleteCampaignSchedule");
        POST("/v1/staff/campaigns/schedule", CampaignScheduleController.class,
                "insertCampaignSchedule");
        PUT("/v1/staff/campaigns/schedule", CampaignScheduleController.class,
                "updateCampaignSchedule");
        GET("/v1/staff/campaigns/schedules", CampaignScheduleController.class,
                "findCampaignSchedules");
        PUT("/v1/staff/campaigns/brand-bidding-detection/upsert",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "upsertBrandBiddingDetectionSetting");
        POST("/v1/staff/campaigns/validate-creatives",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "validateCreatives");
        POST("/v1/staff/campaigns/validate-product-feed",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "validateProductFeed");

        POST("/v1/staff/campaign/budget", CampaignBudgetController.class,
                "createCampaignBudget");
        PUT("/v1/staff/campaign/budget", CampaignBudgetController.class,
                "updateCampaignBudget");
        GET("/v1/staff/campaign/budget", CampaignBudgetController.class,
                "findCampaignBudgets");
        GET("/v1/staff/campaign/budget/{budgetId: \\d+}", CampaignBudgetController.class,
                "findCampaignBudget");
        DELETE("/v1/staff/campaign/budget/{budgetId: \\d+}",
                CampaignBudgetController.class, "deleteCampaignBudget");
        GET("/v1/staff/campaign/influencers-integration-setting/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findInfluencersIntegrationSetting");
        PUT("/v1/staff/campaign/influencers-integration-setting/update",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "updateInfluencersIntegrationSetting");
        PUT("/v1/staff/campaigns/clicks",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "upsertClickCountCsv");
        GET("/v1/staff/campaign/referral-setting/{countryCode: \\w+}",
                CampaignReferralSettingController.class, "findCampaignReferralSetting");
        PUT("/v1/staff/campaign/referral-setting/upsert",
                CampaignReferralSettingController.class, "upsertCampaignReferralSetting");

        PUT("/v1/staff/conversion",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "updateConversionsById");
        PUT("/v1/staff/conversion/automatic-update-rank-setting/calculate",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "updateRankSetting");
        POST("/v1/staff/conversion/csv",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "insertConversionCsv");
        POST("/v1/staff/conversion/mass-approval/update",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "updateStatusAndConfirmedDate");
        PUT("/v1/staff/conversion/mass-approval",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "updateConversionsStatus");
        PUT("/v1/staff/conversion/payment-mass-update",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "insertPaymentMassUpdate");
        PUT("/v1/staff/conversion/rank",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "updateConversionRank");
        PUT("/v1/staff/conversion/reward-edit-date/reward-settings",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "updateConversionsRewardEditDate");
        POST("/v1/staff/conversion/with-click-id",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "insertConversionsWithClickId");
        GET("/v1/staff/automatic-update-conversion-rank-settings/{automaticUpdateConversionRankSettingId: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.staff
                        .AutomaticUpdateConversionRankSettingController.class,
                "findAutomaticUpdateConversionRankSettingDetails");
        GET("/v1/staff/conversion/automatic-update-rank-settings",
                AutomaticUpdateConversionRankSettingController.class,
                "findAutomaticUpdateConversionRankSettings");
        GET("/v1/staff/conversion/automatic-update-conversion-rank-histories/{conversionRankSettingId: \\w+}",
                AutomaticUpdateConversionRankHistoriesController.class,
                "findAutomaticUpdateConversionRankSettingHistories");
        POST("/v1/staff/conversion/automatic-update-rank-settings/create",
                AutomaticUpdateConversionRankSettingController.class,
                "create");
        PUT("/v1/staff/conversion/automatic-update-rank-settings/update",
                AutomaticUpdateConversionRankSettingController.class,
                "updateRankSettings");
        PUT("/v1/staff/conversion/automatic-update-conversion-rank-settings/update-status",
                AutomaticUpdateConversionRankSettingController.class,
                "updateStatusSetting");
        GET("/v1/staff/country/country-code",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findCountryCode");
        GET("/v1/staff/country/global-country-code",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "findGlobalCountryCode");
        GET("/v1/staff/country/is-closeable/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "isCloseable");
        GET("/v1/staff/country/is-allowed-update-payment-status-for-denied-account/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CountryController.class,
                "isAllowedUpdatePaymentStatusForDeniedAccount");
        GET("/v1/staff/country/is-rule-settings-global-sync/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.CountryController.class,
                "isRuleSettingsGlobalSyncEnabled");

        GET("/v1/staff/creative/product-feed/files-name/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "findProductFeedFilesName");
        POST("/v1/staff/creative/product-feed",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "insertProductFeed");
        PUT("/v1/staff/creative/product-feed",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "updateProductFeed");
        GET("/v1/staff/creative/product-feed/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "findProductFeeds");
        PUT("/v1/staff/creative/product-feed/rules-setting",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "upsertProductFeedRulesSetting");
        GET("/v1/staff/creative/product-feed/rules-setting/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "findProductFeedThirdPartyUrls");
        PUT("/v1/staff/creative/update-status",
                jp.ne.interspace.gurkha.controller.v1.staff.CreativeController.class,
                "updateCreativesStatus");

        PUT("/v1/staff/payment/cancel",
                jp.ne.interspace.gurkha.controller.v1.staff.PaymentController.class,
                "cancelPaymentInvoice");
        PUT("/v1/staff/payment/confirm",
                jp.ne.interspace.gurkha.controller.v1.staff.PaymentController.class,
                "confirmPaymentInvoice");
        PUT("/v1/staff/payment/multiple-confirm",
                jp.ne.interspace.gurkha.controller.v1.staff.PaymentController.class,
                "confirmPaymentInvoices");
        POST("/v1/staff/payment/request-invoice",
                jp.ne.interspace.gurkha.controller.v1.staff.PaymentController.class,
                "insertPaymentInvoice");
        POST("/v1/staff/payment/tax-mass/csv",
                jp.ne.interspace.gurkha.controller.v1.staff.ConversionController.class,
                "insertPaymentTaxMassCsv");
        GET("/v1/staff/payment",
                jp.ne.interspace.gurkha.controller.v1.staff.PaymentController.class,
                "findPaymentDetails");
        POST("/v1/staff/payment-history",
                jp.ne.interspace.gurkha.controller.v1.staff.PaymentController.class,
                "findPaymentHistory");
        POST("/v1/staff/product-feed-synchronization",
                ProductFeedSynchronizationController.class, "synchronizeProductFeed");
        GET("/v1/staff/email", StaffAccountController.class, "findStaffEmail");
        POST("/v1/staff/email/send", EmailController.class, "send");
        GET("/v1/staff/email-template", EmailTemplateController.class,
                "findByCountryCodeAndType");
        PUT("/v1/staff/email-template/delivery", EmailTemplateController.class,
                "updateDeliveryByCountryCodeAndType");
        PUT("/v1/staff/email-template/template", EmailTemplateController.class,
                "updateEmailTemplate");
        POST("/v1/staff/email-test/send", EmailTemplateController.class,
                "sendTestEmail");

        GET("/v1/staff/global/campaign/custom-link-rules",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "findCustomLinkRules");
        POST("/v1/staff/global/campaigns",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "createClonedCampaign");
        PUT("/v1/staff/global/campaigns",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "updateClonedCampaign");
        GET("/v1/staff/global/campaigns/synchronizable/{campaignId: \\d+}/country/{countryCode: \\w+}",
                jp.ne.interspace.gurkha.controller.v1.CampaignController.class,
                "isSynchronizable");

        POST("/v1/staff/global/creatives",
                jp.ne.interspace.gurkha.controller.v1.CreativeController.class,
                "createClonedCreatives");
        PUT("/v1/staff/global/creatives",
                jp.ne.interspace.gurkha.controller.v1.CreativeController.class,
                "updateClonedCreatives");
        POST("/v1/staff/global/creatives/custom-link-rules",
                jp.ne.interspace.gurkha.controller.v1.CreativeController.class,
                "insertCreativesCustomLinkRules");
        POST("/v1/staff/global/creatives/groups", CreativeGroupController.class,
                "createClonedCreativeGroups");
        PUT("/v1/staff/global/creatives/groups", CreativeGroupController.class,
                "updateClonedCreativeGroups");
        PUT("/v1/staff/global/creatives/status",
                jp.ne.interspace.gurkha.controller.v1.CreativeController.class,
                "updateClonedCreativeStatus");

        POST("/v1/staff/global/email", EmailController.class, "sendCampaignStatusEmail");

        POST("/v1/staff/global/exchange-rate/sync",
                CurrencyExchangeRateController.class, "syncCurrencyExchangeRate");

        POST("/v1/staff/rewards/settings/category/hourly/regist",
                CategoryRewardSettingController.class,
                "insertRewardPriceSettingHourlyCategory");
        POST("/v1/staff/rewards/settings/category/monthly/regist",
                CategoryRewardSettingController.class,
                "insertRewardPriceSettingMonthlyCategory");
        PUT("/v1/staff/rewards/settings/category/update",
                CategoryRewardSettingController.class,
                "updateCategoryRewardPriceSetting");

        DELETE("/v1/staff/rewards/settings/categories/hourly",
                CategoryRewardSettingController.class,
                "deleteHourlyCategoryRewardSettings");
        DELETE("/v1/staff/rewards/settings/categories/hourly/delete-all",
                CategoryRewardSettingController.class,
                "deleteAllHourlyCategoryRewardSettings");
        DELETE("/v1/staff/rewards/settings/categories/monthly",
                CategoryRewardSettingController.class,
                "deleteMonthlyCategoryRewardSettings");
        DELETE("/v1/staff/rewards/settings/categories/monthly/delete-all",
                CategoryRewardSettingController.class,
                "deleteAllMonthlyCategoryRewardSettings");
        DELETE("/v1/staff/rewards/settings/prices/hourly",
                HourlyRewardPriceSettingController.class, "delete");
        DELETE("/v1/staff/rewards/settings/prices/hourly/delete-all",
                HourlyRewardPriceSettingController.class, "deleteAll");

        GET("/v1/staff/rewards/settings/category", CategoryRewardSettingController.class,
                "findCategoryRewardPriceSettings");
        GET("/v1/staff/rewards/settings/default", RewardSettingController.class,
                "findDefaultRewardPriceSettings");
        POST("/v1/staff/rewards/settings/default/hourly/regist",
                RewardSettingController.class,
                "insertHourlyDefaultRewardPriceSetting");
        GET("/v1/staff/rewards/settings/product", ProductRewardSettingController.class,
                "findProductRewardPriceSettings");
        POST("/v1/staff/rewards/settings/default/monthly/regist",
                RewardSettingController.class,
                "insertMonthlyDefaultRewardPriceSetting");
        POST("/v1/staff/rewards/settings/product/hourly/regist",
                ProductRewardSettingController.class,
                "insertHourlyProductRewardPriceSettings");
        POST("/v1/staff/rewards/settings/product/monthly/regist",
                ProductRewardSettingController.class,
                "insertMonthlyProductRewardPriceSettings");

        POST("/v1/staff/global/rewards/settings/categories",
                GlobalRewardSettingController.class, "syncMonthlyCategoryRewardSettings");
        POST("/v1/staff/global/rewards/settings/categories/hourly",
                GlobalRewardSettingController.class,
                "syncHourlyCategoryRewardSettings");
        PUT("/v1/staff/global/rewards/settings/categories",
                GlobalRewardSettingController.class,
                "updateMonthlyCategoryRewardSetting");

        PUT("/v1/staff/global/rewards/settings/categories/hourly",
                GlobalRewardSettingController.class,
                "updateHourlyCategoryRewardSetting");
        POST("/v1/staff/global/rewards/settings/prices",
                GlobalRewardSettingController.class, "createGlobalRewardPriceSetting");
        POST("/v1/staff/global/rewards/settings/prices/hourly",
                GlobalRewardSettingController.class,
                "createGlobalRewardPriceSettingHourly");
        POST("/v1/staff/global/rewards/settings/prices/hourly-overlap",
                GlobalRewardSettingController.class,
                "createGlobalRewardPriceSettingHourlyOverlap");

        PUT("/v1/staff/rewards/settings/product/update",
                ProductRewardSettingController.class, "updateProductRewardSetting");
        POST("/v1/staff/global/rewards/settings/products",
                GlobalRewardSettingController.class, "insertProductRewardSettings");
        PUT("/v1/staff/global/rewards/settings/products",
                GlobalRewardSettingController.class, "updateProductRewardSettings");

        POST("/v1/staff/global/rewards/settings/products/hourly",
                GlobalRewardSettingController.class, "insertHourlyProductRewardSettings");
        PUT("/v1/staff/global/rewards/settings/products/hourly",
                GlobalRewardSettingController.class, "updateHourlyProductRewardSetting");
        DELETE("/v1/staff/global/rewards/settings/products/hourly/delete",
                ProductRewardSettingController.class,
                "deleteHourlyProductRewardSettings");
        DELETE("/v1/staff/global/rewards/settings/products/hourly/delete-all",
                ProductRewardSettingController.class,
                    "deleteAllHourlyProductRewardSettings");
        DELETE("/v1/staff/global/rewards/settings/products/monthly/delete",
                ProductRewardSettingController.class,
                "deleteMonthlyProductRewardSettings");
        DELETE("/v1/staff/global/rewards/settings/products/monthly/delete-all",
                ProductRewardSettingController.class,
                    "deleteAllMonthlyProductRewardSettings");

        POST("/v1/staff/global/rewards/settings/targets",
                GlobalRewardSettingController.class, "insertResultTargetSetting");
        PUT("/v1/staff/global/rewards/settings/targets",
                GlobalRewardSettingController.class, "updateResultTargetSetting");
        PUT("/v1/staff/global/rewards/settings/targets/use-flag",
                GlobalRewardSettingController.class, "updateResultTargetSettingUseFlag");
        PUT("/v1/staff/global/rewards/settings/targets/partner-invisible-flag",
                GlobalRewardSettingController.class,
                "updateResultTargetSettingPartnerInvisibleFlag");

        GET("/v1/staff/global/rewards/settings/current-total-commission",
                GlobalRewardSettingController.class,
                "findCurrentTotalCommissionRewardPriceSetting");

        GET("/v1/staff/global/should-enable-running-campaign-option/{campaignId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.CampaignController.class,
                "shouldEnableRunningCampaignOption");

        POST("/v1/staff/google-keyword-analytics",
                GoogleKeywordAnalyticController.class, "insertGoogleKeywordAnalytic");
        PUT("/v1/staff/google-keyword-analytics/{id: \\d+}",
                GoogleKeywordAnalyticController.class, "updateGoogleKeywordAnalytic");
        DELETE("/v1/staff/google-keyword-analytics/{id: \\d+}",
                GoogleKeywordAnalyticController.class, "deleteGoogleKeywordAnalytic");
        GET("/v1/staff/google-keyword-analytics/{countryCode: \\w+}",
                GoogleKeywordAnalyticController.class, "findGoogleKeywordAnalytics");

        PUT("/v1/staff/google-keyword-analytics-setting",
                GoogleKeywordAnalyticSettingController.class,
                "update");
        GET("/v1/staff/google-keyword-analytics-setting/{countryCode: \\w+}",
                GoogleKeywordAnalyticSettingController.class,
                "find");

        GET("/v1/staff/merchant-customer-countries",
                MerchantCustomerCountryController.class, "findAll");
        GET("/v1/staff/merchants/names-ids", MerchantAccountController.class,
                "findMerchantNamesAndIds");
        GET("/v1/staff/monthly-closure/closed-months", MonthlyClosureController.class,
                "findTargetMonths");

        GET("/v1/staff/permission/is-all-country-data-access-permitted",
                PermissionController.class, "isAllCountryDataAccessPermitted");
        GET("/v1/staff/permission/check/{permissionName: [a-z\\:\\_]+}",
                PermissionController.class, "hasPermission");
        GET("/v1/staff/publisher-agencies",
                jp.ne.interspace.gurkha.controller.v1.staff
                        .PublisherAgencyController.class,
                "findAll");
        GET("/v1/staff/publishers/names-ids",
                jp.ne.interspace.gurkha.controller.v1.staff
                        .PublisherAccountController.class,
                "findPublisherNamesAndIds");
        GET("/v1/staff/reports/bonus",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findBonusReport");
        GET("/v1/staff/reports/bonus-mapping/{bonusId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findBonusMappingDetails");
        PUT("/v1/staff/reports/bonus/async-export",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "requestBonusReportForAsyncExport");
        GET("/v1/staff/reports/campaign",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findCampaignReport");
        PUT("/v1/staff/reports/campaign/async-export",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "requestCampaignReportForAsyncExport");
        GET("/v1/staff/reports/conversion",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findConversionReport");
        GET("/v1/staff/reports/conversion/download-report",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "downloadConversionReportForStaffCsv");
        PUT("/v1/staff/reports/conversion/async-export",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "requestConversionReportForAsyncExport");
        GET("/v1/staff/reports/daily",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findDailyReport");
        GET("/v1/staff/reports/email",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findEmailReport");
        GET("/v1/staff/reports/site",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "findSiteReport");
        GET("/v1/staff/reports/bank-account-update-history",
                jp.ne.interspace.gurkha.controller.v1.staff.BankAccountController.class,
                "findPublisherBankHistoryReport");
        PUT("/v1/staff/reports/site/async-export",
                jp.ne.interspace.gurkha.controller.v1.staff.ReportController.class,
                "requestSiteReportForAsyncExport");

        GET("/v1/staff/sites/names-ids",
                jp.ne.interspace.gurkha.controller.v1.staff.SiteController.class,
                "findSiteNamesAndIds");
        GET("/v1/staff/sites/{siteId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.SiteController.class,
                "findSiteDetails");
        GET("/v1/staff/world-city-details", WorldCityController.class,
                "findWorldCityDetails");
        GET("/v1/staff/find-cities/{countryCode: \\w+}", WorldCityController.class,
                "findCities");
        GET("/v1/staff/bank-details/{publisherId: \\d+}",
                jp.ne.interspace.gurkha.controller.v1.staff.BankAccountController.class,
                "findBankAccountDetails");

        PUT("/v2/conversions/customer-type",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .ConversionController.class,
                "updateCustomerType");
        POST("/v2/affiliation",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .AffiliationController.class, "addAffiliation");
        PUT("/v2/affiliation/reject", jp.ne.interspace.gurkha.controller.v2.internalteam
                .AffiliationController.class, "rejectAffiliation");
        PUT("/v2/affiliation/status", jp.ne.interspace.gurkha.controller.v2.internalteam
                .AffiliationController.class, "updateAffiliationStatus");
        PUT("/v2/affiliation/mass-approval", jp.ne.interspace.gurkha.controller.v2.internalteam
                .AffiliationController.class, "updateAffiliationMassStatus");
        POST("/v2/campaigns/reward-settings",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .CampaignController.class,
                "calculateRewards");
        GET("/v2/affiliation/publisher-rank",
                PublisherRankController.class,
                "findRank");
        GET("/v2/affiliation/publisher-ranks",
                PublisherRankController.class, "findPublisherRanksPeriod");
        PUT("/v2/conversion/mass-approval",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .ConversionController.class, "updateConversionsStatus");
        POST("/v2/conversion/without-click-id",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .ConversionController.class, "insertConversionsWithoutClickId");
        PUT("/v2/conversions/status",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .ConversionController.class,
                "updateConversionStatus");
        PUT("/v2/conversions/transaction-amount",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .ConversionController.class,
                "updateTransactionAmounts");
        GET("/v2/conversions/commission/waiting-for-calculate/count",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .ConversionController.class,
                "countConversionWaitingCalculate");
        POST("/v2/merchants/account",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .MerchantAccountController.class,
                "createMerchantAccount");
        POST("/v2/merchants/campaign",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .MerchantCampaignController.class,
                "createMerchantCampaign");
        GET("/v2/payment/account-balance/{publisherId: \\d+}",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .PaymentController.class, "findAccountBalance");
        GET("/v2/payment/search",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .PaymentController.class, "findPaymentHistories");
        GET("/v2/payment/publisher-campaign-reward",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .PaymentController.class, "findCampaignRewards");
        POST("/v2/publishers",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .PublisherSignUpController.class,
                "createPublisherAccount");
        POST("/v2/publishers/influencer-data",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .PublisherSignUpController.class,
                "createInfluencerData");
        GET("/v2/rk",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .LinkCodeController.class, "generateRk");
        POST("/v2/sessions/get",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                        .SessionController.class, "find");
        PUT("/v2/sites/status",
                jp.ne.interspace.gurkha.controller.v2.internalteam.SiteController.class,
                "updateSiteStatus");
        POST("/v2/smart-link-tag-sync/site",
                jp.ne.interspace.gurkha.controller.v2.internalteam.SiteController.class,
                "syncSmartLinkTagSiteIds");
        GET("/v2/staff/campaign-statistics", CampaignsStatisticsDataController.class,
                "findCampaignsStatisticsData");
        PUT("/v2/staff/multi-factor-authentication",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .StaffAccountController.class, "updateMfaSecretKey");
        GET("/v2/staff/multi-factor-authentication/enabled/{staffId: \\d+}",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .StaffAccountController.class, "isEnabledMultiFactorAuthentication");
        GET("/v2/staff/multi-factor-authentication/is-required",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .StaffAccountController.class, "isMultiFactorAuthenticationRequired");
        GET("/v2/staff/multi-factor-authentication/qr-code/{staffId: \\d+}",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .StaffAccountController.class, "getQrCode");
        GET("/v2/staff/multi-factor-authentication/validate-one-time-password",
                jp.ne.interspace.gurkha.controller.v2.internalteam
                .StaffAccountController.class, "validateOneTimePassword");
    }
}
