/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.auth.v1;

import java.util.regex.Pattern;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.NonNull;

import ro.pippo.core.route.RouteContext;

import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.BIND_KEY_API_USER_TYPES;
import static lombok.AccessLevel.PACKAGE;

/**
 * API user type checker for the backend of ASEAN AccessTrade.
 *
 * <AUTHOR> Shin
 */
@Singleton
public class ApiUserTypeChecker {

    private static final String USER_TYPE_LOCAL_PARAMETER = "userType";

    @Inject @Named(BIND_KEY_API_USER_TYPES) @VisibleForTesting @Getter(PACKAGE)
    private ImmutableMap<String, ImmutableList<String>> apiUserTypes;

    /**
     * Check and validate the user type by the API from {@link RouteContext}.
     *
     * @param routeContext
     *            {@link RouteContext} containing the HTTP request
     * @return {@code true} if API has no user type or has correct user type,
     *      returns {@code false} if API has wrong user type
     */
    public boolean isValidApiUserType(@NonNull RouteContext routeContext) {
        String localUserType = routeContext.getLocal(USER_TYPE_LOCAL_PARAMETER);
        ImmutableList<String> targetUserType = findUserTypesBy(
                routeContext.getRequestUri());
        return targetUserType.isEmpty() || targetUserType.contains(localUserType);
    }

    @VisibleForTesting
    ImmutableList<String> findUserTypesBy(String uri) {
        return getApiUserTypes().entrySet().stream()
                .filter(map -> Pattern.compile(map.getKey()).matcher(uri).find())
                .map(map -> map.getValue())
                .findFirst()
                .orElse(ImmutableList.of());
    }
}
