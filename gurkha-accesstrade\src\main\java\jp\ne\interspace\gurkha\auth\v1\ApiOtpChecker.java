/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.auth.v1;

import java.time.Instant;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import ro.pippo.core.route.RouteContext;

import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.util.EncryptionUtils;

import static jp.ne.interspace.gurkha.model.AwsSecretKeys.AES_256_OTP_IV;
import static jp.ne.interspace.gurkha.model.AwsSecretKeys.AES_256_OTP_SECRET_KEY;
import static jp.ne.interspace.gurkha.model.ResponseStatus.BAD_REQUEST;
import static jp.ne.interspace.gurkha.model.ResponseStatus.INTERNAL_SERVER_ERROR;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.getSecretValues;

/**
 * API Bank Account checker for the backend.
 *
 * <AUTHOR> Nguyen
 */

@Singleton
public class ApiOtpChecker {

    private static final long OTP_EPOCH_SECONDS_LIMIT = 1800;

    @Inject
    private EncryptionUtils encryptionUtils;

    /**
     * Check and validate the otp token by the API from {@link RouteContext}.
     *
     * @param otpToken timestamp as text encryption
     * @return {@code true} the otp toke is valid,
     *      returns {@code false} the otp toke is invalid
     */
    public boolean isValidOtpToken(String otpToken) {
        try {
            String timeDecryption = encryptionUtils.decryptAes256HashFrom(otpToken,
                    getSecretValues(AES_256_OTP_SECRET_KEY),
                    getSecretValues(AES_256_OTP_IV));
            return !isExpiredToken(Long.valueOf(timeDecryption));
        } catch (NullPointerException ex) {
            throw new GurkhaApplicationException("otpToken cannot be null or empty",
                    BAD_REQUEST);
        } catch (Exception ex) {
            throw new GurkhaApplicationException(
                    "Failed to get data from otp token " + otpToken,
                    INTERNAL_SERVER_ERROR);
        }
    }

    @VisibleForTesting
    boolean isExpiredToken(long time) {
        return getCurrentTimestamp() - time > OTP_EPOCH_SECONDS_LIMIT;
    }

    @VisibleForTesting
    long getCurrentTimestamp() {
        return Instant.now().getEpochSecond();
    }
}
