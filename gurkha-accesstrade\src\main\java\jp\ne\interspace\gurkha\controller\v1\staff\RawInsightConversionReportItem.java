/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import jp.ne.interspace.gurkha.model.ConversionStatus;
import jp.ne.interspace.gurkha.model.DeviceType;
import jp.ne.interspace.gurkha.model.RewardType;
import jp.ne.interspace.gurkha.model.publisher.PostbackStatus;

/**
 * Base class for insight conversion report items.
 *
 * <AUTHOR> <PERSON>ran
 */
@AllArgsConstructor @Getter @EqualsAndHashCode
public class RawInsightConversionReportItem {

    private final long conversionId;
    private final ConversionStatus status;
    private final String verificationId;
    private final String internalTransactionId;
    private final LocalDateTime clickTime;
    private final LocalDateTime conversionTime;
    private final LocalDateTime confirmationTime;
    private final BigDecimal transactionAmount;
    private final BigDecimal discountAmount;
    private final RewardType rewardType;
    private final BigDecimal reward;
    private final BigDecimal atCommission;
    private final BigDecimal merchantAgentCommission;
    private final BigDecimal publisherAgentCommission;
    private final BigDecimal totalCommission;
    private final long campaignId;
    private final int resultId;
    private final String productId;
    private final String productCategoryId;
    private final long siteId;
    private final Integer rank;
    private final long creativeId;
    private final PostbackStatus postbackStatus;
    private final long postbackErrorCount;
    private final LocalDateTime latestPostbackTime;
    private final String postbackUrl;
    private final DeviceType deviceType;
    private final String pointbackId;
    private final String clickReferer;
    private final String clickUrl;
    private final String clickUserAgent;
    private final String customerType;
    private final int transactionItems;
    private final long bonusSettingId;
    private final BigDecimal publisherBonus;
    private final BigDecimal publisherAgentBonus;
    private final BigDecimal merchantAgentBonus;
    private final BigDecimal atBonus;
}
