/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;
import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.publisher.ReferralService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for handling the referral.
 *
 * <AUTHOR>
 */
public class ReferralController extends Controller {

    private static final String CAMPAIGN_ID_PARAMETER = "campaignId";

    @Inject
    private ReferralService referralService;

    /**
     * Insert the referral data of the logged-in publisher.
     */
    public void insertReferral() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        referralService.insertReferral(userId);
        getResponse().ok();
    }

    /**
     * sends the referral campaign ID.
     */
    public void findReferralCampaignId() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER)
                .toString();
        getResponse().json().send(referralService.findReferralCampaignIdBy(countryCode));
    }

    /**
     * Sends the result of checking enabled influencer by country code and campaign id.
     */
    public void isEnabledInfluencer() {
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json()
                .send(referralService.isEnabledInfluencer(campaignId, countryCode));
    }

    /**
     * Sends the result of checking is referral available for current account.
     */
    public void isReferralAvailable() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        getResponse().json().send(referralService
                .isReferralAvailable(userId.getAccountId(), countryCode));
    }
}
