/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.NameValueResponse;
import jp.ne.interspace.gurkha.service.staff.PublisherAgencyService;

/**
 * Controller class for operations on publisher agencies.
 *
 * <AUTHOR>
 */
public class PublisherAgencyController extends Controller {

    @Inject
    private PublisherAgencyService publisherAgencyService;

    /**
     * Sends the {@link NameValueResponse} items of all registered publisher agencies.
     */
    public void findAll() {
        getResponse().json().send(publisherAgencyService.findAll());
    }
}
