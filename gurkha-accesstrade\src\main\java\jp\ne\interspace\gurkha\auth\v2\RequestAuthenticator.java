/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.auth.v2;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.hash.Hashing;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import ro.pippo.core.Request;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.google.common.net.InetAddresses.isInetAddress;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.time.format.DateTimeFormatter.ofPattern;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;
import static jp.ne.interspace.gurkha.config.Environment.PRODUCTION;
import static jp.ne.interspace.gurkha.config.GurkhaConfig.getEnvironment;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.COMMA;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.FORWARDED_FOR;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.BIND_KEY_ALLOWED_IP_ADDRESSES;
import static jp.ne.interspace.gurkha.module.AseanAccessTradePropertiesModule.BIND_KEY_AUTHORIZATION_KEY;

/**
 * Authentication helper for the backend of ASEAN AccessTrade.
 *
 * <AUTHOR> Shin
 */
@Singleton
@Slf4j
public class RequestAuthenticator {

    private static final int AUTHORIZATION_LENGTH = 64;

    private static final DateTimeFormatter FORMATTER = ofPattern("dd-MM-yyyy");

    @Inject @Named(BIND_KEY_AUTHORIZATION_KEY) @VisibleForTesting @Getter
    private String authorizationKey;

    @Inject @Named(BIND_KEY_ALLOWED_IP_ADDRESSES) @VisibleForTesting @Getter
    private ImmutableSet<String> allowedIpAddresses;

    public boolean isValidOn(@NonNull Request request) {
        return isBypassable()
                || (isValidIpAddress(request) && isValidAuthorization(request));
    }

    /**
     * Return client ip address.
     *
     * @param request
     *            The given request
     * @return client ip address
     */
    public String getClientIpFrom(Request request) {
        String ipAddress = null;
        String forwardedFor = request.getHeader(FORWARDED_FOR);
        if (isNullOrEmpty(forwardedFor)) {
            ipAddress = request.getClientIp();
        } else {
            List<String> ipAddresses = Splitter.on(COMMA).splitToList(forwardedFor);
            ipAddress = ipAddresses.get(0).trim();
        }
        return ipAddress;
    }

    @VisibleForTesting
    boolean isBypassable() {
        if (getAllowedIpAddresses().isEmpty() && !isProductionEnvironment()) {
            return true;
        }
        return false;
    }

    @VisibleForTesting
    boolean isValidIpAddress(Request request) {
        String ipAddress = getClientIpFrom(request);
        boolean isValidIpAddress = getAllowedIpAddresses().isEmpty()
                || !isNullOrEmpty(ipAddress) && isInetAddress(ipAddress)
                        && getAllowedIpAddresses().contains(ipAddress);
        if (!isValidIpAddress) {
            log.error("Invalid client IP address: {}", ipAddress);
        }
        return isValidIpAddress;
    }

    @VisibleForTesting
    boolean isValidAuthorization(Request request) {
        String authorizationHeader = request.getHeader(AUTHORIZATION);
        boolean isValidAuthorizationHeader = isWellFormed(authorizationHeader)
                && authorizationHeader.equalsIgnoreCase(
                        generateSha256Hash(getDate(), getAuthorizationKey()));
        if (!isValidAuthorizationHeader) {
            log.error("Invalid authorization header: {}", authorizationHeader);
        }
        return isValidAuthorizationHeader;
    }

    @VisibleForTesting
    String getDate() {
        return ZonedDateTime.now(ZoneOffset.UTC).format(FORMATTER);
    }

    @VisibleForTesting
    boolean isProductionEnvironment() {
        return getEnvironment() == PRODUCTION;
    }

    private boolean isWellFormed(String authorizationHeader) {
        return Strings.nullToEmpty(authorizationHeader).length() == AUTHORIZATION_LENGTH;
    }

    private String generateSha256Hash(String date, String authorizationKey) {
        return Hashing.sha256()
                .hashString(String.join("", date, authorizationKey), UTF_8)
                .toString();
    }
}
