/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.SeoContentConfirmationRequest;
import jp.ne.interspace.gurkha.service.publisher.SeoContentService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;

/**
 * Controller for handling conversions.
 *
 * <AUTHOR>
 */
public class ConversionController extends Controller {

    private static final String USER_ID_LOCAL_PARAMETER = "userId";
    private static final String SITE_ID_PARAMETER = "siteId";
    private static final String CAMPAIGN_ID_PARAMETER = "campaignId";

    @Inject
    private SeoContentService seoContentService;

    /**
     * Inserts the seo content for confirmation to merchant.
     */
    public void requestSeoContentConfirmation() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        String publisherCountryCode = getRouteContext()
                .getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        SeoContentConfirmationRequest request = getRouteContext()
                .createEntityFromBody(SeoContentConfirmationRequest.class);

        seoContentService.requestSeoContentConfirmation(campaignId, siteId, request,
                publisherId, publisherCountryCode);
        getRouteContext().getResponse().ok();
    }
}
