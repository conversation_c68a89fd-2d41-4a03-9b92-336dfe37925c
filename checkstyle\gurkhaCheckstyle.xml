<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN" "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--
    This configuration file was written by the eclipse-cs plugin configuration editor
-->
<!--
    Checkstyle-Configuration: Gurkha Checkstyle Configuration
    Description: none
-->
<module name="Checker">
  <property name="severity" value="error"/>
  <property name="charset" value="UTF-8"/>
  <property name="fileExtensions" value="java, properties, xml"/>
  <module name="TreeWalker">
    <property name="tabWidth" value="4"/>
    <module name="OuterTypeFilename"/>
    <module name="IllegalTokenText">
      <property name="tokens" value="CHAR_LITERAL,STRING_LITERAL"/>
      <property name="format" value="\\u00(08|09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
      <property name="message" value="Avoid using corresponding octal or Unicode escape."/>
    </module>
    <module name="AvoidEscapedUnicodeCharacters">
      <property name="allowEscapesForControlCharacters" value="true"/>
      <property name="allowByTailComment" value="true"/>
      <property name="allowNonPrintableEscapes" value="true"/>
    </module>
    <module name="LineLength">
      <property name="ignorePattern" value="^package.*|^import.*|test\w+|static final|a href|href|http://|https://|ftp://|GET|POST|PUT|DELETE"/>
      <property name="max" value="90"/>
      <property name="tabWidth" value="4"/>
    </module>
    <module name="AvoidStarImport"/>
    <module name="OneTopLevelClass"/>
    <module name="EmptyBlock">
      <property name="option" value="text"/>
      <property name="tokens" value="LITERAL_TRY,LITERAL_FINALLY,LITERAL_IF,LITERAL_ELSE,INSTANCE_INIT,STATIC_INIT,LITERAL_SWITCH"/>
    </module>
    <module name="NeedBraces"/>
    <module name="LeftCurly">
      <property name="maxLineLength" value="90"/>
    </module>
    <module name="RightCurly">
      <metadata name="net.sf.eclipsecs.core.comment" value="Setting for try, catch, finally, if, else"/>
    </module>
    <module name="RightCurly">
      <metadata name="net.sf.eclipsecs.core.comment" value="Setting for class, method, loops, initializers"/>
      <property name="option" value="alone"/>
      <property name="tokens" value="CLASS_DEF,METHOD_DEF,CTOR_DEF,LITERAL_FOR,LITERAL_WHILE,LITERAL_DO,STATIC_INIT,INSTANCE_INIT"/>
    </module>
    <module name="WhitespaceAround">
      <property name="tokens" value="ASSIGN,BAND,BAND_ASSIGN,BOR,BOR_ASSIGN,BSR,BSR_ASSIGN,BXOR,BXOR_ASSIGN,COLON,DIV,DIV_ASSIGN,DO_WHILE,EQUAL,GE,GT,LAND,LCURLY,LE,LITERAL_ASSERT,LITERAL_CATCH,LITERAL_DO,LITERAL_ELSE,LITERAL_FINALLY,LITERAL_FOR,LITERAL_IF,LITERAL_RETURN,LITERAL_SYNCHRONIZED,LITERAL_TRY,LITERAL_WHILE,LOR,LT,MINUS,MINUS_ASSIGN,MOD,MOD_ASSIGN,NOT_EQUAL,PLUS,PLUS_ASSIGN,QUESTION,RCURLY,SL,SLIST,SL_ASSIGN,SR,SR_ASSIGN,STAR,STAR_ASSIGN,TYPE_EXTENSION_AND"/>
      <property name="allowEmptyConstructors" value="true"/>
      <property name="allowEmptyMethods" value="true"/>
      <property name="allowEmptyTypes" value="true"/>
      <property name="allowEmptyLoops" value="true"/>
      <message key="ws.notPreceded" value="WhitespaceAround: ''{0}'' is not preceded with whitespace."/>
      <message key="ws.notFollowed" value="WhitespaceAround: ''{0}'' is not followed by whitespace. Empty blocks may only be represented as '{}' when not part of a multi-block statement (4.1.3)"/>
    </module>
    <module name="OneStatementPerLine"/>
    <module name="MultipleVariableDeclarations"/>
    <module name="ArrayTypeStyle"/>
    <module name="MissingSwitchDefault"/>
    <module name="FallThrough"/>
    <module name="UpperEll"/>
    <module name="ModifierOrder"/>
    <module name="EmptyLineSeparator">
      <property name="tokens" value="IMPORT,CLASS_DEF,ENUM_DEF,INTERFACE_DEF,CTOR_DEF,METHOD_DEF,STATIC_INIT,INSTANCE_INIT,VARIABLE_DEF"/>
      <property name="allowNoEmptyLineBetweenFields" value="true"/>
      <property name="allowMultipleEmptyLines" value="false"/>
    </module>
    <module name="SeparatorWrap">
      <metadata name="net.sf.eclipsecs.core.comment" value="Dots on newline on linewrapping"/>
      <property name="option" value="nl"/>
      <property name="tokens" value="DOT"/>
    </module>
    <module name="SeparatorWrap">
      <metadata name="net.sf.eclipsecs.core.comment" value="Commas on end of line on linewrapping"/>
      <property name="tokens" value="COMMA"/>
    </module>
    <module name="PackageName">
      <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
      <message key="name.invalidPattern" value="Package name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="TypeName">
      <message key="name.invalidPattern" value="Type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="MemberName">
      <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
      <message key="name.invalidPattern" value="Member name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="ParameterName">
      <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
      <message key="name.invalidPattern" value="Parameter name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="LocalVariableName">
      <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
      <property name="allowOneCharVarInForLoop" value="true"/>
      <property name="tokens" value="VARIABLE_DEF"/>
      <message key="name.invalidPattern" value="Local variable name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="ClassTypeParameterName">
      <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)|(^[A-Z][A-Z]?)$"/>
      <message key="name.invalidPattern" value="Class type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="MethodTypeParameterName">
      <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
      <message key="name.invalidPattern" value="Method type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="InterfaceTypeParameterName">
      <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)|(^[A-Z][A-Z]?)$"/>
      <message key="name.invalidPattern" value="Interface type name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="NoFinalizer"/>
    <module name="GenericWhitespace">
      <message key="ws.notPreceded" value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
      <message key="ws.followed" value="GenericWhitespace ''{0}'' is followed by whitespace."/>
      <message key="ws.preceded" value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
      <message key="ws.illegalFollow" value="GenericWhitespace ''{0}'' should followed by whitespace."/>
    </module>
    <module name="Indentation">
      <property name="arrayInitIndent" value="8"/>
      <property name="lineWrappingIndentation" value="8"/>
    </module>
    <module name="AbbreviationAsWordInName">
      <property name="severity" value="warning"/>
      <property name="allowedAbbreviationLength" value="1"/>
      <property name="ignoreFinal" value="false"/>
    </module>
    <module name="OverloadMethodsDeclarationOrder"/>
    <module name="CustomImportOrder">
      <property name="customImportOrderRules" value="STANDARD_JAVA_PACKAGE###THIRD_PARTY_PACKAGE###SAME_PACKAGE(3)###STATIC"/>
      <property name="sortImportsInGroupAlphabetically" value="true"/>
    </module>
    <module name="MethodParamPad"/>
    <module name="OperatorWrap">
      <metadata name="net.sf.eclipsecs.core.comment" value="Operators on newline on linewrapping"/>
      <property name="tokens" value="BAND,BOR,BSR,BXOR,DIV,EQUAL,GE,GT,LAND,LE,LITERAL_INSTANCEOF,LOR,LT,MINUS,MOD,NOT_EQUAL,PLUS,QUESTION,SL,SR,STAR"/>
    </module>
    <module name="AnnotationLocation">
      <property name="tokens" value="CLASS_DEF,INTERFACE_DEF,ENUM_DEF,VARIABLE_DEF"/>
      <property name="allowSamelineMultipleAnnotations" value="true"/>
    </module>
    <module name="NonEmptyAtclauseDescription"/>
    <module name="JavadocTagContinuationIndentation"/>
    <module name="JavadocParagraph">
      <property name="severity" value="ignore"/>
      <metadata name="net.sf.eclipsecs.core.lastEnabledSeverity" value="inherit"/>
    </module>
    <module name="AtclauseOrder">
      <property name="target" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
      <property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
    </module>
    <module name="JavadocMethod">
      <property name="minLineCount" value="2"/>
      <property name="allowedAnnotations" value="Override, Test, VisibleForTesting, BeforeClass, AfterClass"/>
      <property name="scope" value="package"/>
      <property name="allowUndeclaredRTE" value="true"/>
      <property name="allowThrowsTagsForSubclasses" value="true"/>
      <property name="allowMissingPropertyJavadoc" value="true"/>
      <property name="suppressLoadErrors" value="true"/>
      <property name="tokens" value="METHOD_DEF"/>
    </module>
    <module name="MethodName">
      <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9_]*$"/>
      <message key="name.invalidPattern" value="Method name ''{0}'' must match pattern ''{1}''."/>
    </module>
    <module name="SingleLineJavadoc">
      <property name="ignoreInlineTags" value="false"/>
    </module>
    <module name="EmptyCatchBlock">
      <property name="exceptionVariableName" value="expected"/>
    </module>
    <module name="CommentsIndentation"/>
    <module name="UnusedImports"/>
    <module name="RedundantModifier"/>
    <module name="JavadocType"/>
    <module name="Regexp">
      <metadata name="net.sf.eclipsecs.core.comment" value="Trailing whitespace check"/>
      <property name="format" value="[ \t]+$"/>
      <property name="message" value="Trailing whitespace at the end of line"/>
      <property name="illegalPattern" value="true"/>
    </module>
    <module name="Regexp">
      <metadata name="net.sf.eclipsecs.core.comment" value="prohibit importing javax Inject"/>
      <property name="format" value="^import javax.inject.Inject;$"/>
      <property name="message" value="Use com.google.inject.Inject instead of javax.inject.Inject"/>
      <property name="illegalPattern" value="true"/>
    </module>
    <module name="Regexp">
      <metadata name="net.sf.eclipsecs.core.comment" value="prohibit importing google Singleton"/>
      <property name="format" value="^import com.google.inject.Singleton;$"/>
      <property name="message" value="Use javax.inject.Singleton instead of com.google.inject.Singleton"/>
      <property name="illegalPattern" value="true"/>
    </module>
    <module name="ConstantName"/>
    <module name="EmptyStatement"/>
    <module name="EqualsHashCode"/>
    <module name="StringLiteralEquality"/>
    <module name="VisibilityModifier"/>
    <module name="MissingDeprecated"/>
    <module name="AnnotationLocation">
      <property name="tokens" value="METHOD_DEF,CTOR_DEF"/>
    </module>
    <module name="NoWhitespaceAfter">
      <property name="tokens" value="LNOT"/>
    </module>
    <module name="NoWhitespaceBefore"/>
    <module name="JavadocStyle">
      <property name="scope" value="package"/>
      <property name="checkEmptyJavadoc" value="true"/>
      <property name="tokens" value="INTERFACE_DEF,CLASS_DEF,METHOD_DEF,CTOR_DEF"/>
    </module>
    <module name="JavadocMethod">
      <property name="minLineCount" value="2"/>
      <property name="allowUndeclaredRTE" value="true"/>
      <property name="allowThrowsTagsForSubclasses" value="true"/>
      <property name="allowMissingParamTags" value="true"/>
      <property name="allowMissingThrowsTags" value="true"/>
      <property name="allowMissingReturnTag" value="true"/>
      <property name="allowMissingPropertyJavadoc" value="true"/>
      <property name="suppressLoadErrors" value="true"/>
      <property name="tokens" value="CTOR_DEF"/>
    </module>
  </module>
  <module name="FileTabCharacter">
    <property name="eachLine" value="true"/>
  </module>
  <module name="NewlineAtEndOfFile">
    <property name="lineSeparator" value="lf_cr_crlf"/>
    <property name="fileExtensions" value=".java, .xml"/>
  </module>
  <module name="Header">
    <property name="header" value="/**\n * Copyright © 2016 Interspace Co., Ltd. All rights reserved.\n *\n * Licensed under the Interspace's License,\n * you may not use this file except in compliance with the License.\n */"/>
    <property name="ignoreLines" value="2"/>
    <property name="fileExtensions" value="java"/>
  </module>
  <module name="UniqueProperties"/>
  <module name="SuppressionFilter">
    <property name="file" value="${config_loc}/gurkhaCheckstyleSuppressions.xml"/>
  </module>
</module>
