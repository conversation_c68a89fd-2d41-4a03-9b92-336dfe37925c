/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

import com.google.inject.Inject;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.ResponseStatus;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.CreateCustomCreativeRequest;
import jp.ne.interspace.gurkha.model.publisher.CreateCustomCreativesRequest;
import jp.ne.interspace.gurkha.model.publisher.CreateInfluencerCustomCreativeRequest;
import jp.ne.interspace.gurkha.model.publisher.CreativeSummary;
import jp.ne.interspace.gurkha.model.publisher.CreativesAvailableForInfluencersRequest;
import jp.ne.interspace.gurkha.model.publisher.CreativesAvailableForInfluencersResponse;
import jp.ne.interspace.gurkha.model.publisher.CustomCreative;
import jp.ne.interspace.gurkha.model.publisher.CustomCreativeAcceptedUrlResponse;
import jp.ne.interspace.gurkha.model.publisher.FindCustomCreativesRequest;
import jp.ne.interspace.gurkha.model.publisher.ProductFeedResponse;
import jp.ne.interspace.gurkha.model.publisher.QuickLink;
import jp.ne.interspace.gurkha.model.publisher.SpecialCreativeAvailabilityResponse;
import jp.ne.interspace.gurkha.model.publisher.UpdateCustomCreativeForInfluencerRequest;
import jp.ne.interspace.gurkha.persist.model.publisher.CustomCreativesResponse;
import jp.ne.interspace.gurkha.service.publisher.CreativeService;
import jp.ne.interspace.gurkha.service.publisher.CustomCreativeService;

import static java.net.URLDecoder.decode;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.EMPTY;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.UTF_8;

/**
 * Controller for handling creatives.
 *
 * <AUTHOR> href="mailto:<EMAIL>">Jichoul Shin</a>
 */
public class CreativeController extends PagingController {

    private static final String SITE_ID_PARAMETER = "siteId";
    private static final String CAMPAIGN_ID_PARAMETER = "campaignId";
    private static final String CREATIVE_ID_PARAMETER = "creativeId";
    private static final String LANDING_URL_PARAMETER = "landingUrl";
    private static final String CREATIVE_ID_NOT_FOUND_ERROR_MESSAGE =
            "Can't find creative id base on your request";

    @Inject
    private CreativeService creativeService;

    @Inject
    private CustomCreativeService customCreativeService;

    /**
     * Sends the information of text creatives by
     * {@code campaignId}, {@code siteId} to the client.
     */
    public void findTextCreatives() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();

        getRouteContext().json().send(creativeService.findTextCreatives(campaignId,
                siteId, publisherId.getAccountId()));
    }

    /**
     * Sends the information of image creatives by
     * {@code campaignId}, {@code siteId} to the client.
     */
    public void findImageCreatives() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();

        getRouteContext().json().send(creativeService.findImageCreativeGroups(campaignId,
                siteId, publisherId.getAccountId()));
    }

    /**
     * Sends the information of seo content creatives by
     * {@code campaignId}, {@code siteId} to the client.
     */
    public void findSeoContentCreatives() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();

        getRouteContext().json().send(creativeService.findSeoContentCreatives(campaignId,
                siteId, publisherId.getAccountId()));
    }

    /**
     * Checks whether special creatives are available for the given campaign and sends the
     * result to the client side.
     */
    public void checkSpecialCreativeAvailabilityForCampaign() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();

        SpecialCreativeAvailabilityResponse response = creativeService
                .checkSpecialCreativeAvailabilityFor(campaignId, siteId,
                        publisherId.getAccountId());

        getRouteContext().json().send(response);
    }

    /**
     * Sends the information of {@link CreativeSummary}s
     * for the given campaign and optional site to the client.
     */
    public void findCreativeSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();

        getRouteContext().json().send(creativeService.findCreativeSummaries(campaignId,
                siteId, publisherId.getAccountId()));
    }

    /**
     * Sends the {@link CreativeSummary}s of the custom creatives added by the
     * logged-in publisher for the given site to the client.
     */
    public void findCustomCreativeSummaries() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();

        getRouteContext().json().send(customCreativeService
                .findCustomCreativeSummaries(siteId, publisherId.getAccountId()));
    }

    /**
     * Sends the {@link CustomCreative}s for the given campaign and site to the client.
     *
     * @throws UnsupportedEncodingException
     *             when a non-existent encoding is used for the creation of the image HTML
     *             code of the custom creatives
     */
    public void findCustomCreatives() throws UnsupportedEncodingException {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        FindCustomCreativesRequest request = getRouteContext()
                .createEntityFromParameters(FindCustomCreativesRequest.class);
        CustomCreativesResponse result = customCreativeService.findCustomCreatives(siteId,
                publisherId, campaignId, countryCode, request);
        getResponse().json().send(result);
    }

    /**
     * Sends the accepted custom creative urls for the given campaign to the client side.
     */
    public void findCustomCreativeAcceptedUrlsForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();

        CustomCreativeAcceptedUrlResponse response = customCreativeService
                .findCustomCreativeAcceptedUrlsFor(siteId, publisherId.getAccountId(),
                        campaignId);

        getResponse().json().send(response);
    }

    /**
     * Creates a new custom creative for the logged-in publisher with the given site and
     * campaign.
     *
     * @throws UnsupportedEncodingException
     *             when a non-existent encoding is used for the creation of the image HTML
     *             code of the created custom creative
     */
    public void createCustomCreativeForPublisher() throws UnsupportedEncodingException {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        CreateCustomCreativeRequest request = getRouteContext()
                .createEntityFromBody(CreateCustomCreativeRequest.class);
        getResponse().json().send(customCreativeService.createCustomCreative(siteId,
                campaignId, publisherId, request, countryCode));
    }

    /**
     * Sends the data for the legacy product feeds of a campaign to the client side.
     */
    public void findLegacyProductFeeds() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        List<ProductFeedResponse> response = creativeService
                .findLegacyProductFeeds(siteId, campaignId, publisherId, countryCode);

        getResponse().json().send(response);
    }

    /**
     * Sends the quick link of a campaign to the client side.
     */
    public void findQuickLink() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        QuickLink response = creativeService.findQuickLinkBy(siteId,
                publisherId.getAccountId(), campaignId);

        getResponse().json().send(response);
    }

    /**
     * Finds the creatives available for influencers.
     */
    public void findCreativesAvailableForInfluencers() {
        CreativesAvailableForInfluencersRequest request = getRouteContext()
                .createEntityFromParameters(
                        CreativesAvailableForInfluencersRequest.class);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        List<CreativesAvailableForInfluencersResponse> response = creativeService
                .findCreativesAvailableForInfluencers(request, countryCode);
        getResponse().json(response);
    }

    /**
     * Finds the first ID of custom creative.
     *
     *  @throws UnsupportedEncodingException
     *          throws unsupport encoding exception
     */
    public void findFirstId() throws UnsupportedEncodingException {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        String landingUrl = decode(
                getRouteContext().getParameter(LANDING_URL_PARAMETER).toString(EMPTY),
                UTF_8);
        UserId publisher = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        if (siteId == 0 || campaignId == 0 || landingUrl.isEmpty() || Objects.isNull(
                publisher)) {
            throw new GurkhaApplicationException(CREATIVE_ID_NOT_FOUND_ERROR_MESSAGE,
                    ResponseStatus.NOT_FOUND);
        }
        getResponse().json(
                customCreativeService.findFirstId(siteId, campaignId, landingUrl,
                        publisher.getAccountId()));
    }

    /**
     * Sends the original creatives of a campaign to the client side.
     */
    public void findOriginalCreatives() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        getResponse().json(creativeService.findOriginalCreativeDetails(campaignId,
                publisherId.getAccountId(), siteId));
    }

    /**
     * Sends the base64 image of creatives.
     */
    public void findBase64EncodedImage() {
        long creativeId = getRouteContext().getParameter(CREATIVE_ID_PARAMETER).toLong();
        getResponse().json().send(creativeService.findBase64EncodedImageBy(creativeId));
    }

    /**
     * Creates a new custom creative for influencer with product links url and name.
     */
    public void createCustomCreativeForInfluencer() {
        CreateInfluencerCustomCreativeRequest request =
                getRouteContext().createEntityFromBody(
                        CreateInfluencerCustomCreativeRequest.class);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        customCreativeService.createInfluencerCustomCreative(siteId, userId, request);
        getResponse().ok();
    }

    /**
     * Updates the custom creative for influencer with the request.
     */
    public void updateCustomCreativeForInfluencer() {
        UpdateCustomCreativeForInfluencerRequest request =
                getRouteContext().createEntityFromBody(
                        UpdateCustomCreativeForInfluencerRequest.class);
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        customCreativeService.updateInfluencerCustomCreative(siteId, userId, request);
        getResponse().ok();
    }

    /**
     * Finds the custom creatives for influencer with the request.
     *
     * @throws Exception
     *             the campaign ID does not find by creative ID
     */
    public void findCustomCreativesForInfluencer() throws Exception {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        getResponse().json(customCreativeService.findInfluencerCustomCreatives(siteId,
                userId.getAccountId()));
    }

    /**
     * Finds the custom creative detail by given request.
     */
    public void findCustomCreativeDetailsForInfluencer() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long creativeId = getRouteContext()
                .getParameter(CREATIVE_ID_PARAMETER).toLong();
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        getResponse().json(customCreativeService
                .findInfluencerCustomCreative(
                        creativeId, siteId, userId.getAccountId()));
    }

    /**
     * Creates the multiple custom creatives by the given request.
     *
     * @throws Exception
     *          failed to create custom creatives
     */
    public void createCustomCreatives() throws Exception {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER).toLong();
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        CreateCustomCreativesRequest request = getRouteContext()
                .createEntityFromBody(CreateCustomCreativesRequest.class);
        getResponse().json(customCreativeService.createCustomCreatives(userId, siteId,
                campaignId, request, countryCode));
    }
}
