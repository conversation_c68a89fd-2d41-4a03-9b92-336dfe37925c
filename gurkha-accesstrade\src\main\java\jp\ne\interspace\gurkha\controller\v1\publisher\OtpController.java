/**
 * Copyright © 2023 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.util.Locale;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.publisher.OtpService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.LOCALE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.OTP_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller class for operations on publisher otp.
 *
 * <AUTHOR>
 */
public class OtpController extends Controller {

    @Inject
    private OtpService otpService;

    /**
     * Generate OTP token to client by given publisher ID.
     */
    public void generateOtpToken() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String otp = getRouteContext().getParameter(OTP_PARAMETER).toString();
        getRouteContext().json()
                .send(otpService.generateOtpToken(publisherId.getAccountId(), otp));
    }

    /**
     * Handles OTP request by given publisher ID and country code.
     */
    public void requestOtp() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        Locale locale = getRouteContext().getLocal(LOCALE_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        otpService.handleOtpRequest(publisherId.getAccountId(), locale, countryCode);
        getResponse().ok();
    }
}
