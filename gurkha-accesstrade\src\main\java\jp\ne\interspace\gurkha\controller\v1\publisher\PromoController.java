/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.PromoType;
import jp.ne.interspace.gurkha.service.publisher.PromoService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling promo data.
 *
 * <AUTHOR>
 */
public class PromoController extends Controller {

    @Inject
    private PromoService promoService;

    /**
     * Finds publisher promo data.
     */
    public void findPromos() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        long siteId = getRequest().getQueryParameter("siteId").toLong(0);
        long campaignId = getRequest().getQueryParameter("campaignId").toLong(0);
        PromoType promoType = getRequest().getQueryParameter("type").to(PromoType.class);
        String promoCategory = getRequest().getQueryParameter("promoCategory")
                .toString(null);
        int size = getRequest().getQueryParameter("size").toInt();
        int startIndex = getRequest().getQueryParameter("startIndex").toInt();
        getResponse().json(promoService.findPromos(countryCode, size, startIndex,
                publisherId.getAccountId(), siteId, campaignId, promoType,
                promoCategory));
    }

    /**
     * Sends the promo categories for the given criteria to the client side.
     */
    public void findPromoCategories() {
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        long campaignId = getRequest().getQueryParameter("campaignId").toLong(0);
        long siteId = getRequest().getQueryParameter("siteId").toLong(0);
        UserId user = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PromoType promoType = getRequest().getQueryParameter("type").to(PromoType.class);
        getRouteContext().json().send(promoService.findPromoCategories(countryCode,
                campaignId, siteId, user.getAccountId(), promoType));
    }
}
