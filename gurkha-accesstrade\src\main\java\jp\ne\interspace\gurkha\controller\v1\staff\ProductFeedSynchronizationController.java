/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.staff.ProductFeedSynchronizationRequest;
import jp.ne.interspace.gurkha.service.staff.ProductFeedSynchronizationService;

/**
 * Controller for handling product feed synchronization.
 *
 * <AUTHOR> Mayur
 */
public class ProductFeedSynchronizationController extends Controller {

    @Inject
    private ProductFeedSynchronizationService productFeedSynchronizationService;

    /**
     * synchronize product feed.
     */
    public void synchronizeProductFeed() {
        ProductFeedSynchronizationRequest request = getRouteContext()
                .createEntityFromBody(ProductFeedSynchronizationRequest.class);
        productFeedSynchronizationService.synchronizeProductFeedBy(request);
        getRouteContext().getResponse().ok();
    }

}
