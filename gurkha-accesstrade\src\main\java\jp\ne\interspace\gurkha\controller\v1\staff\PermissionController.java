/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;
import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.internal.staff.StaffPermissionService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for handling permissions.
 *
 * <AUTHOR>
 */
public class PermissionController extends Controller {

    private static final String PERMISSION_NAME_PARAMETER = "permissionName";

    @Inject
    private StaffPermissionService staffPermissionService;

    /**
     * Sends {@code true} if staff has global access data permission, otherwise
     * {@code false}.
     *
     * @throws Exception when something unusual occurs
     */
    public void isAllCountryDataAccessPermitted() throws Exception {
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getResponse().json().send(staffPermissionService
                .isAllCountryDataAccessPermitted(staff.getAccountId()));
    }

    /**
     * Returns {@code true} if staff has given permission, otherwise
     * {@code false}.
     *
     * @throws Exception when something unusual occurs
     */
    public void hasPermission() throws Exception {
        String permissionName = getRouteContext().getParameter(PERMISSION_NAME_PARAMETER)
                .toString();
        UserId staff = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getResponse().json().send(staffPermissionService
                .hasPermission(permissionName, staff.getAccountId()));
    }
}
