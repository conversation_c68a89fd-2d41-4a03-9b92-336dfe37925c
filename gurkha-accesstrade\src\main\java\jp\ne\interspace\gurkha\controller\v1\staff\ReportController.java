/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.Collections;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.model.PagingRequest;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.staff.BonusReportDetail;
import jp.ne.interspace.gurkha.model.staff.BonusReportRequest;
import jp.ne.interspace.gurkha.model.staff.BonusReportResponse;
import jp.ne.interspace.gurkha.model.staff.CampaignReportRequest;
import jp.ne.interspace.gurkha.model.staff.ConversionReportRequest;
import jp.ne.interspace.gurkha.model.staff.DailyReport;
import jp.ne.interspace.gurkha.model.staff.DailyReportRequest;
import jp.ne.interspace.gurkha.model.staff.EmailReportItem;
import jp.ne.interspace.gurkha.model.staff.FindBonusReportRequest;
import jp.ne.interspace.gurkha.model.staff.FindEmailReportsRequest;
import jp.ne.interspace.gurkha.model.staff.SiteReportRequest;
import jp.ne.interspace.gurkha.service.staff.BonusReportService;
import jp.ne.interspace.gurkha.service.staff.CampaignReportService;
import jp.ne.interspace.gurkha.service.staff.ConversionReportService;
import jp.ne.interspace.gurkha.service.staff.DateBasedReportService;
import jp.ne.interspace.gurkha.service.staff.EmailReportService;
import jp.ne.interspace.gurkha.service.staff.SiteReportService;

import static com.google.common.base.Strings.isNullOrEmpty;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller layer for the report APIs.
 *
 * <AUTHOR> Ferreras
 */
public class ReportController extends PagingController {

    private static final String BONUS_ID_PARAMETER = "bonusId";
    private static final int DEFAULT_BONUS_REPORT_DETAILS_COUNT = 0;

    @Inject
    private ConversionReportService conversionReportService;

    @Inject
    private CampaignReportService campaignReportService;

    @Inject
    private SiteReportService siteReportService;

    @Inject
    private DateBasedReportService dateBasedReportService;

    @Inject
    private EmailReportService service;

    @Inject
    private BonusReportService bonusReportService;

    /**
     * Sends the conversion report for the given criteria to the client side.
     */
    public void findConversionReport() {
        PagingRequest pagingRequest = getPagingRequest();
        ConversionReportRequest request = getRouteContext()
                .createEntityFromParameters(ConversionReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        getRouteContext().json().send(conversionReportService.findConversionReport(
                request, pagingRequest));
    }

    /**
     * Sends the conversion report for the given criteria to the client side.
     */
    public void downloadConversionReportForStaffCsv() {
        ConversionReportRequest request = getRouteContext()
                .createEntityFromParameters(ConversionReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        getRouteContext().json().send(conversionReportService
                .downloadConversionReportForStaffCsv(request));
    }

    /**
     * Sends the campaign report for the given criteria to the client side.
     */
    public void findCampaignReport() {
        CampaignReportRequest request = getRouteContext().createEntityFromParameters(
                CampaignReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        getRouteContext().json().send(campaignReportService.findCampaignReport(request));
    }

    /**
     * Sends the site report for the given criteria to the client side.
     */
    public void findSiteReport() {
        SiteReportRequest request = getRouteContext().createEntityFromParameters(
                SiteReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        getRouteContext().json().send(siteReportService.findSiteReport(request));
    }

    /**
     * Sends the daily report for the given criteria to the client side.
     */
    public void findDailyReport() {
        DailyReportRequest request = getRouteContext().createEntityFromParameters(
                DailyReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        DailyReport dailyReport = dateBasedReportService.findDailyReport(request);
        getRouteContext().json().send(dailyReport);
    }

    /**
     * Sends the email reports to the client.
     */
    public void findEmailReport() {
        FindEmailReportsRequest request = getRouteContext().createEntityFromParameters(
                FindEmailReportsRequest.class);
        List<EmailReportItem> items = service.findEmailReportBy(request);
        getResponse().json().send(items);
    }

    /**
     * Sends the bonus report for the given criteria to the client side.
     */
    public void findBonusReport() {
        PagingRequest pagingRequest = getPagingRequest();
        FindBonusReportRequest request = getRouteContext()
                .createEntityFromParameters(FindBonusReportRequest.class);
        int totalBonusReportDetail = bonusReportService.summarizeBonusReport(request);
        List<BonusReportDetail> bonusReportDetails =
                totalBonusReportDetail > DEFAULT_BONUS_REPORT_DETAILS_COUNT
                        ? bonusReportService.findBonusReport(request, pagingRequest)
                        : Collections.emptyList();
        getRouteContext().json().send(createBonusReportResponse(totalBonusReportDetail,
                bonusReportDetails));
    }

    /**
     * Sends the bonus mapping details for the given criteria to the client side.
     */
    public void findBonusMappingDetails() {
        long bonusId = getRouteContext().getRequest().getPathParameter(BONUS_ID_PARAMETER)
                .toLong();
        getRouteContext().json()
                .send(bonusReportService.findBonusMappingDetails(bonusId));
    }

    /**
     * request conversion report to be exported asynchronously.
     */
    public void requestConversionReportForAsyncExport() {
        ConversionReportRequest request = getRouteContext()
                .createEntityFromBody(ConversionReportRequest.class);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long staffId = publisherId.getAccountId();
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        conversionReportService.requestAsyncExportBy(request, staffId);
        getRouteContext().getResponse().ok();
    }

    /**
     * request campaign report to be exported asynchronously.
     */
    public void requestCampaignReportForAsyncExport() {
        CampaignReportRequest request = getRouteContext().createEntityFromBody(
                CampaignReportRequest.class);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long staffId = publisherId.getAccountId();
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        campaignReportService.requestAsyncExportBy(request, staffId);
        getRouteContext().getResponse().ok();
    }

    /**
     * Requests site report to be exported asynchronously.
     */
    public void requestSiteReportForAsyncExport() {
        SiteReportRequest request = getRouteContext().createEntityFromBody(
                SiteReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        siteReportService.requestAsyncExport(request, userId.getAccountId());
        getRouteContext().getResponse().ok();
    }

    /**
     * Requests bonus report to be exported asynchronously.
     */
    public void requestBonusReportForAsyncExport() {
        BonusReportRequest request = getRouteContext().createEntityFromBody(
                BonusReportRequest.class);
        request.setCountryCode(getCountryCodeOrDefault(request.getCountryCode()));
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        bonusReportService.requestAsyncExport(request, userId.getAccountId());
        getRouteContext().getResponse().ok();
    }

    @VisibleForTesting
    String getCountryCodeOrDefault(String desiredCountryCode) {
        return !isNullOrEmpty(desiredCountryCode)
                ? desiredCountryCode
                : getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
    }

    @VisibleForTesting
    BonusReportResponse createBonusReportResponse(int totalBonusReportDetail,
            List<BonusReportDetail> bonusReportDetails) {
        return new BonusReportResponse(totalBonusReportDetail, bonusReportDetails);
    }
}
