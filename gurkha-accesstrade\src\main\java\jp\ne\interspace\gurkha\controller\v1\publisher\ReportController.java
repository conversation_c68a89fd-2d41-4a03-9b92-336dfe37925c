/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;

import jp.ne.interspace.gurkha.controller.v1.PagingController;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.PagingRequest;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.CampaignReportItem;
import jp.ne.interspace.gurkha.model.publisher.CampaignReportRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionCampaignReport;
import jp.ne.interspace.gurkha.model.publisher.ConversionCampaignReportResponse;
import jp.ne.interspace.gurkha.model.publisher.ConversionGroupedByTransactionReportDetail;
import jp.ne.interspace.gurkha.model.publisher.ConversionGroupedByTransactionReportRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportAsyncExportRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportDetails;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportDownloadCsvRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportLocalDateTimeRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportProductRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportRequest;
import jp.ne.interspace.gurkha.model.publisher.ConversionReportSummaryRequest;
import jp.ne.interspace.gurkha.model.publisher.CreativeReportItem;
import jp.ne.interspace.gurkha.model.publisher.CreativeReportRequest;
import jp.ne.interspace.gurkha.model.publisher.CustomReportItem;
import jp.ne.interspace.gurkha.model.publisher.CustomReportRequest;
import jp.ne.interspace.gurkha.model.publisher.DailyDeviceReportItem;
import jp.ne.interspace.gurkha.model.publisher.DailyDeviceReportRequest;
import jp.ne.interspace.gurkha.model.publisher.EngagementSummaryReportRequest;
import jp.ne.interspace.gurkha.model.publisher.EngagementSummaryReportResponse;
import jp.ne.interspace.gurkha.model.publisher.FindRewardReportRequest;
import jp.ne.interspace.gurkha.model.publisher.MonthlyDeviceReportItem;
import jp.ne.interspace.gurkha.model.publisher.MonthlyDeviceReportRequest;
import jp.ne.interspace.gurkha.model.publisher.MonthlyReportItem;
import jp.ne.interspace.gurkha.model.publisher.MonthlyReportRequest;
import jp.ne.interspace.gurkha.model.publisher.PagingResponse;
import jp.ne.interspace.gurkha.model.publisher.PublisherConversionCampaignSummaryItem;
import jp.ne.interspace.gurkha.model.publisher.RefereeCampaignReportRequest;
import jp.ne.interspace.gurkha.model.publisher.RefereeReportRequest;
import jp.ne.interspace.gurkha.model.publisher.ReportResponse;
import jp.ne.interspace.gurkha.model.publisher.RewardSummaryReportResponse;
import jp.ne.interspace.gurkha.model.publisher.SubIdReportItem;
import jp.ne.interspace.gurkha.model.publisher.SubIdReportRequest;
import jp.ne.interspace.gurkha.model.publisher.SummaryReportRequest;
import jp.ne.interspace.gurkha.model.publisher.SummaryReportResponse;
import jp.ne.interspace.gurkha.service.publisher.CampaignBasedReportService;
import jp.ne.interspace.gurkha.service.publisher.ConversionReportService;
import jp.ne.interspace.gurkha.service.publisher.DateBasedReportService;
import jp.ne.interspace.gurkha.service.publisher.DeviceReportService;
import jp.ne.interspace.gurkha.service.publisher.RefereeReportService;
import jp.ne.interspace.gurkha.service.publisher.SummaryReportService;

import static java.time.ZonedDateTime.parse;
import static java.time.format.DateTimeFormatter.ISO_DATE_TIME;
import static javax.ws.rs.core.HttpHeaders.CONTENT_DISPOSITION;
import static javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.model.ResponseStatus.BAD_REQUEST;
import static jp.ne.interspace.gurkha.model.ResponseStatus.INTERNAL_SERVER_ERROR;
import static org.apache.commons.codec.CharEncoding.UTF_8;

/**
 * Controller layer for the report APIs.
 *
 * <AUTHOR> Shin
 */
@Slf4j
public class ReportController extends PagingController {

    private static final String FAILED_TO_DOWNLOAD_CONVERSION_REPORT_CSV = "Failed to download conversion report csv.";
    private static final String SITE_ID_PARAMETER = "siteId";
    private static final String INVOICE_ID_PARAMETER = "invoiceId";
    private static final Map<String, String> FILE_NAME_FORMATS = new ImmutableMap.Builder<String, String>()
            .put("en", "Conversion_Report_%s_%s.csv")
            .put("id", "Laporan_Konversi_%s_%s.csv")
            .put("ja", "成果レポート_%s_%s.csv")
            .put("th", "รายงานคอนเวอร์ชั่น_%s_%s.csv")
            .put("vi", "Báo_cáo_thành_quả_%s_%s.csv")
            .put("zh_TW", "交易報表_%s_%s.csv").build();
    private static final String CONTENT_DISPOSITION_ATTACHMENT = "attachment; filename=\"%s\"";
    private static final String CSV_CONTENT_TYPE = "application/csv; charset=" + UTF_8;
    private static final int MAXIMUM_PERIOD = 7;
    private static final String FAILED_DATE_RANGE =
            "Date range exceeded. Please select a period of up to %d days.";
    private static final String PERIOD_DAYS_REQUIRED = "Period date is required.";

    @Inject
    private DateBasedReportService dateBasedReportService;

    @Inject
    private DeviceReportService deviceReportService;

    @Inject
    private ConversionReportService conversionReportService;

    @Inject
    private CampaignBasedReportService campaignBasedReportService;

    @Inject
    private RefereeReportService refereeReportService;

    @Inject
    private SummaryReportService summaryReportService;

    /**
     * Sends the custom report for the given criteria to the client side.
     */
    public void findCustomReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        CustomReportRequest request = getRouteContext().createEntityFromParameters(
                CustomReportRequest.class);
        ReportResponse<CustomReportItem> response =
                dateBasedReportService.findCustomReport(publisherId.getAccountId(),
                        request);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the daily device report for the given criteria to the client side.
     */
    public void findDailyDeviceReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        DailyDeviceReportRequest request = getRouteContext()
                .createEntityFromParameters(DailyDeviceReportRequest.class);

        List<DailyDeviceReportItem> response = deviceReportService
                .findDailyDeviceReportFor(publisherId.getAccountId(), request);
        getRouteContext().json().send(response);
    }

    /**
     * Sends the monthly report for the given criteria to the client side.
     */
    public void findMonthlyReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        MonthlyReportRequest request = getRouteContext()
                .createEntityFromParameters(MonthlyReportRequest.class);

        ReportResponse<MonthlyReportItem> response =
                dateBasedReportService.findMonthlyReport(publisherId.getAccountId(),
                        request);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the monthly device report for the given criteria to the client side.
     */
    public void findMonthlyDeviceReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        MonthlyDeviceReportRequest request = getRouteContext()
                .createEntityFromParameters(MonthlyDeviceReportRequest.class);

        List<MonthlyDeviceReportItem> response = deviceReportService
                .findMonthlyDeviceReportFor(publisherId.getAccountId(), request);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the conversion report for the given criteria to the client side.
     */
    public void findConversionReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);

        ConversionReportRequest request;
        try {
            request = getRouteContext().createEntityFromParameters(
                    ConversionReportRequest.class);
        } catch (Exception e) {
            getLogger().debug(
                    "Failed to bind parameters directly, attempting manual binding", e);
            request = new ConversionReportRequest();

            ConversionReportLocalDateTimeRequest localDateTimeRequest =
                    getRouteContext().createEntityFromParameters(
                            ConversionReportLocalDateTimeRequest.class);
            request.setSiteId(localDateTimeRequest.getSiteId());
            request.setCampaignId(localDateTimeRequest.getCampaignId());
            request.setInvoiceNumber(localDateTimeRequest.getInvoiceNumber());
            request.setConversionStatuses(localDateTimeRequest.getConversionStatuses());
            request.setPeriodBase(localDateTimeRequest.getPeriodBase());
            request.setPendingForMerchantPayment(
                    localDateTimeRequest.isPendingForMerchantPayment());
            request.setMerchantPaymentApproved(
                    localDateTimeRequest.isMerchantPaymentApproved());
            request.setCustomerType(localDateTimeRequest.getCustomerType());
        }
        String fromDateStr = getRouteContext().getParameter("fromDate").toString(null);
        String toDateStr = getRouteContext().getParameter("toDate").toString(null);

        if (fromDateStr != null && toDateStr != null) {
            try {
                ZonedDateTime fromDate = parseStringToDate(fromDateStr);
                ZonedDateTime toDate = parseStringToDate(toDateStr);

                request.setFromDate(fromDate);
                request.setToDate(toDate);
            } catch (Exception e) {
                getLogger().warn("Failed to parse date strings: {} and {}", fromDateStr,
                        toDateStr, e);
            }
            validatePeriodDays(request);
        }
        ConversionReportDetails response = conversionReportService
                .findConversionReportFor(publisherId.getAccountId(), request);

        getRouteContext().json().send(response);
    }



    /**
     * Parses a date string in format yyyy-MM-ddT00:00:00+HH:00 or yyyy-MM-ddT00:00:00
     * and returns a ZonedDateTime ignoring any timezone offset.
     */
    @VisibleForTesting
    ZonedDateTime parseStringToDate(String inputDateString) {
        String datePart = inputDateString;
        if (inputDateString.contains("+")) {
            datePart = inputDateString.substring(0, inputDateString.indexOf("+"));
        }

        return parse(datePart + "Z", ISO_DATE_TIME);
    }

    /**
     * Sends the conversion report for the given criteria to the client side.
     */
    public void downloadConversionReportForPublisherCsv() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ConversionReportDownloadCsvRequest request = getRouteContext()
                .createEntityFromParameters(ConversionReportDownloadCsvRequest.class);

        try {
            HttpServletResponse response = getRouteContext().getResponse()
                    .getHttpServletResponse();
            setHeaders(response, request.getFromDate().toLocalDate(),
                    request.getToDate().toLocalDate(), request.getLocale());
            conversionReportService.downloadConversionReportForPublisherCsv(
                    publisherId.getAccountId(), request, response.getOutputStream());
        } catch (Exception ex) {
            getLogger().error(FAILED_TO_DOWNLOAD_CONVERSION_REPORT_CSV, ex);
            throw new GurkhaApplicationException(FAILED_TO_DOWNLOAD_CONVERSION_REPORT_CSV,
                    INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Sends the campaign report for the given criteria to the client side.
     */
    public void findCampaignReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        CampaignReportRequest request = getRouteContext()
                .createEntityFromParameters(CampaignReportRequest.class);
        List<CampaignReportItem> response = campaignBasedReportService
                .findCampaignReportFor(publisherId.getAccountId(), request);
        getRouteContext().json().send(response);
    }

    /**
     * Sends the campaign report for the given criteria to the client side.
     */
    public void findConversionCampaignReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PagingRequest pagingRequest = getPagingRequest();
        ConversionReportSummaryRequest request = getRouteContext()
                .createEntityFromParameters(ConversionReportSummaryRequest.class);
        ConversionCampaignReport conversionCampaign = campaignBasedReportService
                .findConversionReportSummaries(publisherId.getAccountId(), request);
        List<PublisherConversionCampaignSummaryItem> conversionCampaigns =
                campaignBasedReportService.findConversionCampaignSummaryReport(
                        publisherId.getAccountId(), request, pagingRequest);
        getRouteContext().json().send(createConversionCampaignReportResponse(
                conversionCampaign, conversionCampaigns));
    }

    /**
     * Sends the summary report for the given publisher to the client side.
     */
    public void findSummaryReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        SummaryReportRequest request = getRouteContext()
                .createEntityFromParameters(SummaryReportRequest.class);

        SummaryReportResponse response = summaryReportService
                .findSummaryReportFor(publisherId.getAccountId(), request);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the creative report for the given criteria to the client side.
     */
    public void findCreativeReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        CreativeReportRequest request = getRouteContext()
                .createEntityFromParameters(CreativeReportRequest.class);
        List<CreativeReportItem> response = campaignBasedReportService
                .findCreativeReportFor(publisherId.getAccountId(), request);
        getRouteContext().json().send(response);
    }

    /**
     * Sends the conversion grouped transaction report for the given criteria to the
     * client side.
     */
    public void findConversionGroupedByTransactionReport() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ConversionGroupedByTransactionReportRequest request = getRouteContext()
                .createEntityFromParameters(
                        ConversionGroupedByTransactionReportRequest.class);
        List<ConversionGroupedByTransactionReportDetail> response =
                conversionReportService.findConversionGroupedByTransactionReport(
                        userId.getAccountId(), request);
        getRouteContext().json().send(response);
    }

    /**
     * Sends the engagement summary report for the given publisher to the client side.
     */
    public void findEngagementSummaryReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        EngagementSummaryReportRequest request = getRouteContext()
                .createEntityFromParameters(EngagementSummaryReportRequest.class);

        EngagementSummaryReportResponse response = summaryReportService
                .findEngagementSummaryReportFor(publisherId.getAccountId(), request);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the reward summary report for the given publisher to the client side.
     */
    public void findRewardSummaryReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        RewardSummaryReportResponse response = summaryReportService
                .findRewardSummaryReportFor(publisherId.getAccountId(), countryCode);

        getRouteContext().json().send(response);
    }

    /**
     * Sends the reward report for the given publisher to the client side.
     */
    public void findRewardReport() {
        FindRewardReportRequest request = getRouteContext()
                .createEntityFromParameters(FindRewardReportRequest.class);
        getRouteContext().json().send(
                conversionReportService.findRewardReport(request));
    }

    /**
     * Sends the referee report for the given criteria to the client side.
     */
    public void findRefereeReport() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        RefereeReportRequest request = getRouteContext()
                .createEntityFromParameters(RefereeReportRequest.class);
        getRouteContext().json().send(refereeReportService
                .findRefereeReportFor(publisherId.getAccountId(), request));
    }

    /**
     * Sends the referee campaign report for the given criteria to the client side.
     */
    public void findRefereeCampaignReport() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        RefereeCampaignReportRequest request = getRouteContext()
                .createEntityFromParameters(RefereeCampaignReportRequest.class);
        getRouteContext().json().send(refereeReportService
                .findRefereeCampaignReportFor(publisherId.getAccountId(), request));
    }

    /**
     * Request conversion report to be exported asynchronously.
     */
    public void requestConversionReportForAsyncExport() {
        ConversionReportAsyncExportRequest request = getRouteContext()
                .createEntityFromBody(ConversionReportAsyncExportRequest.class);
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        conversionReportService.requestAsyncExportBy(request, publisherId.getAccountId());
        getRouteContext().getResponse().ok();
    }

    /**
     * Sends the sub id report base on the given criteria to the client side.
     */
    public void findSubIdReportForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        SubIdReportRequest request = getRouteContext()
                .createEntityFromParameters(SubIdReportRequest.class);

        List<SubIdReportItem> response = campaignBasedReportService
                .findSubIdReportFor(publisherId.getAccountId(), request);
        getRouteContext().json().send(response);
    }

    /**
     * Sends the conversion report summary.
     */
    public void findConversionReportSummary() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        getRouteContext().json().send(
                conversionReportService
                        .findConversionReportSummary(publisherId.getAccountId(), siteId));
    }

    /**
     * Sends the conversion report details.
     */
    public void findConversionReportDetails() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PagingRequest pagingRequest = getPagingRequest();
        ConversionReportRequest request = getRouteContext()
                .createEntityFromParameters(ConversionReportRequest.class);
        getRouteContext().json().send(conversionReportService.findConversionReportDetails(
                publisherId.getAccountId(), request, pagingRequest));
    }

    /**
     * Sends the conversion report products.
     */
    public void findProducts() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        ConversionReportProductRequest request = getRouteContext()
                .createEntityFromParameters(ConversionReportProductRequest.class);
        getRouteContext().json()
                .send(conversionReportService.findConversionReportProducts(
                        publisherId.getAccountId(), request));
    }

    /**
     * Finds the from paid date and to paid date by the given invoice id.
     */
    public void findMinMaxPaidDate() {
        String invoiceId = getRouteContext().getParameter(INVOICE_ID_PARAMETER)
                .toString();
        getRouteContext().json()
                .send(conversionReportService.findMinMaxPaidDate(invoiceId));
    }

    @VisibleForTesting
    void setHeaders(HttpServletResponse response, LocalDate fromDate, LocalDate toDate,
            String locale) {
        String responseFileName = String.format(FILE_NAME_FORMATS.get(locale), fromDate,
                toDate.minusDays(1));
        response.setHeader(CONTENT_DISPOSITION,
                String.format(CONTENT_DISPOSITION_ATTACHMENT, responseFileName));
        response.setHeader(CONTENT_TYPE, CSV_CONTENT_TYPE);
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }

    @VisibleForTesting
    PagingResponse<PublisherConversionCampaignSummaryItem> createPagingResponse(
            PagingRequest pagingRequest,
            List<PublisherConversionCampaignSummaryItem> conversionCampaigns,
            long totalNumberOfItems) {
        return createPagingResponseData(pagingRequest, conversionCampaigns,
                (int) totalNumberOfItems);
    }

    @VisibleForTesting
    ConversionCampaignReportResponse createConversionCampaignReportResponse(
            ConversionCampaignReport conversionCampaign,
            List<PublisherConversionCampaignSummaryItem> conversionCampaigns) {
        return new ConversionCampaignReportResponse(conversionCampaign.getCampaignCount(),
                conversionCampaign.getConversionCount(),
                conversionCampaign.getTotalReward(), conversionCampaign.getCurrency(),
                conversionCampaigns);
    }

    @VisibleForTesting
    void validatePeriodDays(ConversionReportRequest request) {
        if ( request.getFromDate() == null || request.getToDate() == null) {
            throw new GurkhaApplicationException( PERIOD_DAYS_REQUIRED, BAD_REQUEST);
        }
        if (request.getToDate().minusDays(MAXIMUM_PERIOD)
                .isAfter(request.getFromDate())) {
            throw new GurkhaApplicationException(
                    String.format(FAILED_DATE_RANGE, MAXIMUM_PERIOD), BAD_REQUEST);
        }
    }
}
