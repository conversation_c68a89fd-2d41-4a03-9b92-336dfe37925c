/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.List;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.staff.MerchantCustomerCountry;
import jp.ne.interspace.gurkha.service.staff.MerchantCustomerCountryService;

/**
 * Controller layer for handling the merchant customer country.
 *
 * <AUTHOR>
 */
public class MerchantCustomerCountryController extends Controller {

    @Inject
    private MerchantCustomerCountryService merchantCustomerCountryService;

    /**
     * Sends all merchant customer countries.
     */
    public void findAll() {
        List<MerchantCustomerCountry> response = merchantCustomerCountryService.findAll();
        getResponse().json().send(response);
    }
}
