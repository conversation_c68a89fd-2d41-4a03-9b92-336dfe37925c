/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import java.time.YearMonth;
import java.util.List;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.PaymentProcessType;
import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.service.publisher.PaymentService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller for publisher payment.
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON></a>
 */
public class PaymentController extends Controller {

    private static final String FROM_MONTH_PARAMETER = "fromMonth";
    private static final String TO_MONTH_PARAMETER = "toMonth";
    private static final String TARGET_INVOICE_NUMBERS_PARAMETER = "targetInvoiceNumbers";
    private static final String INVOICE_NUMBER_PARAMETER = "invoiceNumber";
    private static final String LIMIT_PARAMETER = "limit";
    private static final String INVOICE_ID_PARAMETER = "invoiceId";
    private static final String TYPE_PARAMETER = "type";

    @Inject
    private PaymentService paymentService;

    /**
     * Find publisher payment data for payment page on publisher dashboard.
     */
    public void findMonthlyPayments() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        getRouteContext().json().send(paymentService.findMonthlyPayments(
                userId.getAccountId(),
                YearMonth.parse(
                        getRouteContext().getParameter(FROM_MONTH_PARAMETER).toString()),
                YearMonth.parse(
                        getRouteContext().getParameter(TO_MONTH_PARAMETER).toString()),
                getRouteContext().getParameter(INVOICE_NUMBER_PARAMETER).toString(),
                countryCode));
    }

    /**
     * Sends an invoice of selected dates to the client side.
     */
    public void downloadInvoice() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);

        List<String> targetInvoiceNumbers = getRouteContext()
                .getParameter(TARGET_INVOICE_NUMBERS_PARAMETER).toList();

        paymentService.generateInvoice(userId.getAccountId(), countryCode,
                targetInvoiceNumbers, getRouteContext().getResponse());
    }

    /**
     * Find paid payment data.
     */
    public void findPaidPayments() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        String fromMonth = getRouteContext().getParameter(FROM_MONTH_PARAMETER)
                .toString();
        getRouteContext().json()
                .send(paymentService.findPaidPayments(userId.getAccountId(),
                        fromMonth != null ? YearMonth.parse(fromMonth) : null,
                        YearMonth.parse(getRouteContext().getParameter(TO_MONTH_PARAMETER)
                                .toString()),
                        getRouteContext().getParameter(LIMIT_PARAMETER).toInt()));
    }

    /**
     * Find payment process stage details.
     */
    public void findPaymentProcessStageDetails() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        PaymentProcessType type = getRouteContext().getParameter(TYPE_PARAMETER)
                .to(PaymentProcessType.class);
        getRouteContext().json()
                .send(paymentService.findPaymentProcessData(userId.getAccountId(), type));
    }

    /**
     * Finds payment summary by the given search criteria.
     */
    public void findPaymentSummary() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json()
                .send(paymentService.findPaymentSummary(userId.getAccountId()));
    }

    /**
     * Finds invoice details by the given invoice id.
     */
    public void findInvoiceDetails() {
        UserId userId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json().send(paymentService.findInvoiceDetails(
                userId.getAccountId(),
                getRouteContext().getParameter(INVOICE_ID_PARAMETER).toString()));
    }
}
