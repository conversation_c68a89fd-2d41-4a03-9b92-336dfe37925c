/**
 * Copyright © 2016 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.UserId;
import jp.ne.interspace.gurkha.model.publisher.PostbackDetails;
import jp.ne.interspace.gurkha.model.publisher.PostbackParameterConversionDataType;
import jp.ne.interspace.gurkha.model.publisher.SubId;
import jp.ne.interspace.gurkha.model.publisher.SubIdResponse;
import jp.ne.interspace.gurkha.model.publisher.UpsertPostbackDetailsRequest;
import jp.ne.interspace.gurkha.model.publisher.UpsertSiteRequest;
import jp.ne.interspace.gurkha.model.publisher.UpsertSiteResponse;
import jp.ne.interspace.gurkha.model.publisher.UpsertSubIdsRequest;
import jp.ne.interspace.gurkha.service.publisher.PostbackService;
import jp.ne.interspace.gurkha.service.publisher.SiteService;
import jp.ne.interspace.gurkha.service.publisher.SubIdService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;
import static jp.ne.interspace.gurkha.config.GurkhaConstants.USER_ID_LOCAL_PARAMETER;

/**
 * Controller class for operations on publisher sites.
 *
 * <AUTHOR> Varga
 */
public class SiteController extends Controller {

    private static final String SITE_ID_PARAMETER = "siteId";
    private static final String SITE_URL_PARAMETER = "siteUrl";

    @Inject
    private SiteService siteService;

    @Inject
    private SubIdService subIdService;

    @Inject
    private PostbackService postbackService;

    /**
     * Sends all non-deleted websites of the logged-in publisher to the client side.
     */
    public void findNonDeletedSiteSummariesForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        getRouteContext().json()
                .send(siteService.findAllSummariesFor(publisherId.getAccountId()));
    }

    /**
     * Sends the details of the requested non-deleted website of the logged-in publisher
     * to the client side.
     */
    public void findNonDeletedSiteDetailsForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();

        getRouteContext().json().send(siteService.findDetailsForSingleBy(siteId,
                publisherId.getAccountId()));
    }

    /**
     * Send the smart link tag details by given site ID to the client side.
     */
    public void findSmartLinkTagKeyDetails() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        getRouteContext().json().send(siteService.findSmartLinkTagKeyDetails(siteId));
    }

    /**
     * Generates smart link tag key by given site ID and send smart link tag key details
     * to client.
     */
    public void generateSmartLinkTagKey() {
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        getRouteContext().json().send(siteService.generateSmartLinkTagKey(siteId));
    }

    /**
     * Creates a new website for the logged-in publisher, or updates an existing one based
     * on the incoming request body.
     */
    public void upsertSiteForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        UpsertSiteRequest request = getRouteContext().getRequest()
                .createEntityFromBody(UpsertSiteRequest.class);
        String countryCode = getRouteContext().getLocal(COUNTRY_CODE_LOCAL_PARAMETER);
        UpsertSiteResponse response = siteService.upsert(publisherId, request,
                countryCode);

        getRouteContext().json().send(response);
    }

    /**
     * Deletes a website of the logged-in publisher.
     */
    public void deleteSiteForPublisher() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();

        siteService.delete(siteId, publisherId);

        getRouteContext().getResponse().ok();
    }

    /**
     * Checks given site URL is available access or not.
     */
    public void isAccessibleUrl() {
        String siteUrl = getRouteContext().getParameter(SITE_URL_PARAMETER).toString();
        getRouteContext().json().send(siteService.isAccessibleUrl(siteUrl));
    }

    /**
     * Sends the sub IDs of the requested non-deleted website of the logged-in publisher
     * to the client side.
     */
    public void findSubIdsForNonDeletedSite() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();

        SubIdResponse response = subIdService.findSubIdsBy(siteId,
                publisherId.getAccountId());

        getRouteContext().json().send(response);
    }

    /**
     * Upserts the given {@link SubId}s for the given non-deleted website of the logged-in
     * publisher.
     */
    public void upsertSubIdsForNonDeletedSite() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UpsertSubIdsRequest request = getRequest()
                .createEntityFromBody(UpsertSubIdsRequest.class);

        subIdService.upsertSubIdsBy(siteId, publisherId, request);

        getResponse().ok();
    }

    /**
     * Sends the postback details of the requested non-deleted website of the logged-in
     * publisher to the client side.
     */
    public void findPostbackDetailsForNonDeletedSite() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();

        PostbackDetails response = postbackService
                .findPostbackDetailsBy(siteId, publisherId.getAccountId());

        getRouteContext().json().send(response);
    }

    /**
     * Upserts the {@link PostbackDetails} for the given non-deleted website of the
     * logged-in publisher.
     */
    public void upsertPostbackDetailsForNonDeletedSite() {
        UserId publisherId = getRouteContext().getLocal(USER_ID_LOCAL_PARAMETER);
        long siteId = getRouteContext().getParameter(SITE_ID_PARAMETER).toLong();
        UpsertPostbackDetailsRequest request = getRequest()
                .createEntityFromBody(UpsertPostbackDetailsRequest.class);

        postbackService.upsertPostbackDetailsBy(siteId, publisherId, request);

        getResponse().ok();
    }

    /**
     * Returns all available {@link PostbackParameterConversionDataType}s to the client.
     */
    public void findAllPostbackParameterConversionDataTypes() {
        getRouteContext().json()
                .send(postbackService.findAllPostbackParameterConversionDataTypes());
    }

    /**
     * Returns all available postback sub id parameters to the client.
     */
    public void findAllPostbackSubIds() {
        getRouteContext().json().send(postbackService.findAllPostbackSubIds());
    }
}
