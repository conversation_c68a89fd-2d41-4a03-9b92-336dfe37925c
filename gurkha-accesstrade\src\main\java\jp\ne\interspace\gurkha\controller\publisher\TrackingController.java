/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.publisher;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.service.publisher.TrackingService;

import static jp.ne.interspace.gurkha.config.GurkhaConstants.COUNTRY_CODE_LOCAL_PARAMETER;

/**
 * Controller for handling tracking.
 *
 * <AUTHOR>
 */
public class TrackingController extends Controller {

    private static final String TRACKING_NAME_PARAMETER = "trackingName";

    @Inject
    private TrackingService trackingService;

    /**
     * Sends the enabled tracking details for landing page
     * based on the given criteria to the client.
     */
    public void findEnabledLandingPageTrackingDetails() {
        String countryCode = getRouteContext().getParameter(COUNTRY_CODE_LOCAL_PARAMETER)
                .toString();
        String trackingName = getRouteContext().getParameter(TRACKING_NAME_PARAMETER)
                .toString();
        getRouteContext().json().send(trackingService
                .findEnabledLandingPageTrackingDetailsBy(countryCode, trackingName));
    }

    /**
     * Sends the enabled tracking details for conversion
     * based on the given criteria to the client.
     */
    public void findEnabledConversionTrackingDetails() {
        String countryCode = getRouteContext().getParameter(COUNTRY_CODE_LOCAL_PARAMETER)
                .toString();
        String trackingName = getRouteContext().getParameter(TRACKING_NAME_PARAMETER)
                .toString();
        getRouteContext().json().send(trackingService
                .findEnabledConversionTrackingDetailsBy(countryCode, trackingName));
    }
}
