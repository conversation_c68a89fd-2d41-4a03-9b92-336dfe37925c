/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.staff.UpdateCampaignAutoActionApprovedDurationsRequest;
import jp.ne.interspace.gurkha.service.staff.ResultService;

/**
 * Controller for handling result target setting.
 *
 * <AUTHOR>
 */
public class ResultTargetSettingController extends Controller {

    private static final String CAMPAIGN_ID_PARAMETER = "campaignId";

    @Inject
    private ResultService resultService;

    /**
     * Sends the campaign auto action approved durations.
     */
    public void findCampaignAutoActionApprovedDurations() {
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER)
                .toLong();
        getResponse().json().send(
                resultService.findCampaignAutoActionApprovedDurationsBy(campaignId));
    }

    /**
     * Updates the campaign auto action approved durations.
     */
    public void updateCampaignAutoActionApprovedDurations() {
        UpdateCampaignAutoActionApprovedDurationsRequest request =
                getRouteContext().createEntityFromBody(
                        UpdateCampaignAutoActionApprovedDurationsRequest.class);
        resultService.updateCampaignAutoActionApprovedDurationsBy(request);
        getResponse().ok();
    }

    /**
     * Sends the campaign result target settings by the given campaign ID.
     */
    public void findCampaignResultTargetSettings() {
        long campaignId = getRouteContext().getParameter(CAMPAIGN_ID_PARAMETER)
                .toLong();
        getResponse().json().send(resultService.findCampaignResultTargetSettings(
                campaignId));
    }
}
