/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.merchant;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.auth.v1.RequestAuthenticator;
import jp.ne.interspace.gurkha.exception.GurkhaApplicationException;
import jp.ne.interspace.gurkha.model.ResponseStatus;
import jp.ne.interspace.gurkha.model.UserSecret;

/**
 * Controller for merchant authentication.
 *
 * <AUTHOR> OBS DEV Team
 */
public class MerchantAuthenticationController extends Controller {

    @Inject
    private RequestAuthenticator authenticator;

    /**
     * Authenticates merchant credentials and sends user uid and secret in JSON response
     * if succeeded, or a 401 response if failed.
     * <p>
     * the authorization header must contain a SHA256-hashed string that composed of
     * user's credentials as described by the following pseudo code:
     * </p>
     * {@code SHA256(USERNAME + ':' + MD5(PASSWORD))}
     */
    public void authenticateCredentials() {
        UserSecret secret = authenticator
                .getMerchantSecretFrom(getRouteContext().getRequest());
        if (secret != null) {
            getRouteContext().json().send(secret);
        } else {
            throw new GurkhaApplicationException("Invalid merchant credentials!",
                    ResponseStatus.UNAUTHORIZED);
        }
    }

}
