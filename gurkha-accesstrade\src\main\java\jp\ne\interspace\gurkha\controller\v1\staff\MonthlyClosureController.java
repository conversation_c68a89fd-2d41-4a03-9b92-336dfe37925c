/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.gurkha.controller.v1.staff;

import java.util.List;

import com.google.inject.Inject;

import ro.pippo.controller.Controller;

import jp.ne.interspace.gurkha.model.ClosedTargetMonthOfCountry;
import jp.ne.interspace.gurkha.service.MonthlyClosureService;

/**
 * Controller for handling monthly closure.
 *
 * <AUTHOR>
 */
public class MonthlyClosureController extends Controller {

    @Inject
    private MonthlyClosureService monthlyCloureService;

    /**
     * Sends monthly closed target months of each countries.
     */
    public void findTargetMonths() {
        List<ClosedTargetMonthOfCountry> response = monthlyCloureService
                .findTargetMonths();
        getRouteContext().json().send(response);
    }
}
